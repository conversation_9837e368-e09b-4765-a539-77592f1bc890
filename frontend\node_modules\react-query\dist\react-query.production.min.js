!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t=t||self).ReactQuery={},t.React)}(this,(function(t,e){"use strict";var n="default"in e?e.default:e;function r(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i=function(){function t(){this.listeners=[]}var e=t.prototype;return e.subscribe=function(t){var e=this,n=t||function(){};return this.listeners.push(n),this.onSubscribe(),function(){e.listeners=e.listeners.filter((function(t){return t!==n})),e.onUnsubscribe()}},e.hasListeners=function(){return this.listeners.length>0},e.onSubscribe=function(){},e.onUnsubscribe=function(){},t}();function u(){return(u=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var s="undefined"==typeof window;function o(){}function a(t){return"number"==typeof t&&t>=0&&t!==1/0}function c(t){return Array.isArray(t)?t:[t]}function l(t,e){return t.filter((function(t){return-1===e.indexOf(t)}))}function h(t,e){return Math.max(t+(e||0)-Date.now(),0)}function f(t,e,n){return R(t)?"function"==typeof e?u({},n,{queryKey:t,queryFn:e}):u({},e,{queryKey:t}):t}function d(t,e,n){return R(t)?[u({},e,{queryKey:t}),n]:[t||{},e]}function v(t,e){var n=t.active,r=t.exact,i=t.fetching,u=t.inactive,s=t.predicate,o=t.queryKey,a=t.stale;if(R(o))if(r){if(e.queryHash!==y(o,e.options))return!1}else if(!b(e.queryKey,o))return!1;var c=function(t,e){return!0===t&&!0===e||null==t&&null==e?"all":!1===t&&!1===e?"none":(null!=t?t:!e)?"active":"inactive"}(n,u);if("none"===c)return!1;if("all"!==c){var l=e.isActive();if("active"===c&&!l)return!1;if("inactive"===c&&l)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(("boolean"!=typeof i||e.isFetching()===i)&&!(s&&!s(e)))}function p(t,e){var n=t.exact,r=t.fetching,i=t.predicate,u=t.mutationKey;if(R(u)){if(!e.options.mutationKey)return!1;if(n){if(m(e.options.mutationKey)!==m(u))return!1}else if(!b(e.options.mutationKey,u))return!1}return("boolean"!=typeof r||"loading"===e.state.status===r)&&!(i&&!i(e))}function y(t,e){return((null==e?void 0:e.queryKeyHashFn)||m)(t)}function m(t){var e,n=c(t);return e=n,JSON.stringify(e,(function(t,e){return O(e)?Object.keys(e).sort().reduce((function(t,n){return t[n]=e[n],t}),{}):e}))}function b(t,e){return function t(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(e&&n&&"object"==typeof e&&"object"==typeof n)return!Object.keys(n).some((function(r){return!t(e[r],n[r])}));return!1}(c(t),c(e))}function g(t,e){if(t===e)return t;var n=Array.isArray(t)&&Array.isArray(e);if(n||O(t)&&O(e)){for(var r=n?t.length:Object.keys(t).length,i=n?e:Object.keys(e),u=i.length,s=n?[]:{},o=0,a=0;a<u;a++){var c=n?a:i[a];s[c]=g(t[c],e[c]),s[c]===t[c]&&o++}return r===u&&o===r?t:s}return e}function O(t){if(!C(t))return!1;var e=t.constructor;if(void 0===e)return!0;var n=e.prototype;return!!C(n)&&!!n.hasOwnProperty("isPrototypeOf")}function C(t){return"[object Object]"===Object.prototype.toString.call(t)}function R(t){return"string"==typeof t||Array.isArray(t)}function P(t){Promise.resolve().then(t).catch((function(t){return setTimeout((function(){throw t}))}))}function q(){if("function"==typeof AbortController)return new AbortController}var Q=new(function(t){function e(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!s&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},e}r(e,t);var n=e.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},n.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t((function(t){"boolean"==typeof t?n.setFocused(t):n.onFocus()}))},n.setFocused=function(t){this.focused=t,t&&this.onFocus()},n.onFocus=function(){this.listeners.forEach((function(t){t()}))},n.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},e}(i)),E=new(function(t){function e(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!s&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},e}r(e,t);var n=e.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},n.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t((function(t){"boolean"==typeof t?n.setOnline(t):n.onOnline()}))},n.setOnline=function(t){this.online=t,t&&this.onOnline()},n.onOnline=function(){this.listeners.forEach((function(t){t()}))},n.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},e}(i));function S(t){return Math.min(1e3*Math.pow(2,t),3e4)}function F(t){return"function"==typeof(null==t?void 0:t.cancel)}var M=function(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent};function w(t){return t instanceof M}var x=function(t){var e,n,r,i,u=this,s=!1;this.abort=t.abort,this.cancel=function(t){return null==e?void 0:e(t)},this.cancelRetry=function(){s=!0},this.continueRetry=function(){s=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(t,e){r=t,i=e}));var o=function(e){u.isResolved||(u.isResolved=!0,null==t.onSuccess||t.onSuccess(e),null==n||n(),r(e))},a=function(e){u.isResolved||(u.isResolved=!0,null==t.onError||t.onError(e),null==n||n(),i(e))};!function r(){if(!u.isResolved){var i;try{i=t.fn()}catch(t){i=Promise.reject(t)}e=function(t){if(!u.isResolved&&(a(new M(t)),null==u.abort||u.abort(),F(i)))try{i.cancel()}catch(t){}},u.isTransportCancelable=F(i),Promise.resolve(i).then(o).catch((function(e){var i,o;if(!u.isResolved){var c,l=null!=(i=t.retry)?i:3,h=null!=(o=t.retryDelay)?o:S,f="function"==typeof h?h(u.failureCount,e):h,d=!0===l||"number"==typeof l&&u.failureCount<l||"function"==typeof l&&l(u.failureCount,e);if(!s&&d)u.failureCount++,null==t.onFail||t.onFail(u.failureCount,e),(c=f,new Promise((function(t){setTimeout(t,c)}))).then((function(){if(!Q.isFocused()||!E.isOnline())return new Promise((function(e){n=e,u.isPaused=!0,null==t.onPause||t.onPause()})).then((function(){n=void 0,u.isPaused=!1,null==t.onContinue||t.onContinue()}))})).then((function(){s?a(e):r()}));else a(e)}}))}}()},A=new(function(){function t(){this.queue=[],this.transactions=0,this.notifyFn=function(t){t()},this.batchNotifyFn=function(t){t()}}var e=t.prototype;return e.batch=function(t){var e;this.transactions++;try{e=t()}finally{this.transactions--,this.transactions||this.flush()}return e},e.schedule=function(t){var e=this;this.transactions?this.queue.push(t):P((function(){e.notifyFn(t)}))},e.batchCalls=function(t){var e=this;return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.schedule((function(){t.apply(void 0,r)}))}},e.flush=function(){var t=this,e=this.queue;this.queue=[],e.length&&P((function(){t.batchNotifyFn((function(){e.forEach((function(e){t.notifyFn(e)}))}))}))},e.setNotifyFunction=function(t){this.notifyFn=t},e.setBatchNotifyFunction=function(t){this.batchNotifyFn=t},t}()),D=console;function U(){return D}var I=function(){function t(t){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=t.meta,this.scheduleGc()}var e=t.prototype;return e.setOptions=function(t){var e;this.options=u({},this.defaultOptions,t),this.meta=null==t?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(e=this.options.cacheTime)?e:3e5)},e.setDefaultOptions=function(t){this.defaultOptions=t},e.scheduleGc=function(){var t=this;this.clearGcTimeout(),a(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){t.optionalRemove()}),this.cacheTime))},e.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},e.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},e.setData=function(t,e){var n,r,i=this.state.data,u=function(t,e){return"function"==typeof t?t(e):t}(t,i);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,i,u))?u=i:!1!==this.options.structuralSharing&&(u=g(i,u)),this.dispatch({data:u,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt}),u},e.setState=function(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})},e.cancel=function(t){var e,n=this.promise;return null==(e=this.retryer)||e.cancel(t),n?n.then(o).catch(o):Promise.resolve()},e.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},e.reset=function(){this.destroy(),this.setState(this.initialState)},e.isActive=function(){return this.observers.some((function(t){return!1!==t.options.enabled}))},e.isFetching=function(){return this.state.isFetching},e.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(t){return t.getCurrentResult().isStale}))},e.isStaleByTime=function(t){return void 0===t&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!h(this.state.dataUpdatedAt,t)},e.onFocus=function(){var t,e=this.observers.find((function(t){return t.shouldFetchOnWindowFocus()}));e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.onOnline=function(){var t,e=this.observers.find((function(t){return t.shouldFetchOnReconnect()}));e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.addObserver=function(t){-1===this.observers.indexOf(t)&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},e.removeObserver=function(t){-1!==this.observers.indexOf(t)&&(this.observers=this.observers.filter((function(e){return e!==t})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},e.getObserversCount=function(){return this.observers.length},e.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},e.fetch=function(t,e){var n,r,i,u=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var s;return null==(s=this.retryer)||s.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){var o=this.observers.find((function(t){return t.options.queryFn}));o&&this.setOptions(o.options)}var a=c(this.queryKey),l=q(),h={queryKey:a,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(l)return u.abortSignalConsumed=!0,l.signal}});var f,d,v={fetchOptions:e,options:this.options,queryKey:a,state:this.state,fetchFn:function(){return u.options.queryFn?(u.abortSignalConsumed=!1,u.options.queryFn(h)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(f=this.options.behavior)||f.onFetch(v));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=v.fetchOptions)?void 0:r.meta))||this.dispatch({type:"fetch",meta:null==(d=v.fetchOptions)?void 0:d.meta});return this.retryer=new x({fn:v.fetchFn,abort:null==l||null==(i=l.abort)?void 0:i.bind(l),onSuccess:function(t){u.setData(t),null==u.cache.config.onSuccess||u.cache.config.onSuccess(t,u),0===u.cacheTime&&u.optionalRemove()},onError:function(t){w(t)&&t.silent||u.dispatch({type:"error",error:t}),w(t)||(null==u.cache.config.onError||u.cache.config.onError(t,u),U().error(t)),0===u.cacheTime&&u.optionalRemove()},onFail:function(){u.dispatch({type:"failed"})},onPause:function(){u.dispatch({type:"pause"})},onContinue:function(){u.dispatch({type:"continue"})},retry:v.options.retry,retryDelay:v.options.retryDelay}),this.promise=this.retryer.promise,this.promise},e.dispatch=function(t){var e=this;this.state=this.reducer(this.state,t),A.batch((function(){e.observers.forEach((function(e){e.onQueryUpdate(t)})),e.cache.notify({query:e,type:"queryUpdated",action:t})}))},e.getDefaultState=function(t){var e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==t.initialData?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,r=void 0!==e;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},e.reducer=function(t,e){var n,r;switch(e.type){case"failed":return u({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return u({},t,{isPaused:!0});case"continue":return u({},t,{isPaused:!1});case"fetch":return u({},t,{fetchFailureCount:0,fetchMeta:null!=(n=e.meta)?n:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return u({},t,{data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(r=e.dataUpdatedAt)?r:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var i=e.error;return w(i)&&i.revert&&this.revertState?u({},this.revertState):u({},t,{error:i,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return u({},t,{isInvalidated:!0});case"setState":return u({},t,e.state);default:return t}},t}(),T=function(t){function e(e){var n;return(n=t.call(this)||this).config=e||{},n.queries=[],n.queriesMap={},n}r(e,t);var n=e.prototype;return n.build=function(t,e,n){var r,i=e.queryKey,u=null!=(r=e.queryHash)?r:y(i,e),s=this.get(u);return s||(s=new I({cache:this,queryKey:i,queryHash:u,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(i),meta:e.meta}),this.add(s)),s},n.add=function(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"queryAdded",query:t}))},n.remove=function(t){var e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter((function(e){return e!==t})),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"queryRemoved",query:t}))},n.clear=function(){var t=this;A.batch((function(){t.queries.forEach((function(e){t.remove(e)}))}))},n.get=function(t){return this.queriesMap[t]},n.getAll=function(){return this.queries},n.find=function(t,e){var n=d(t,e)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find((function(t){return v(n,t)}))},n.findAll=function(t,e){var n=d(t,e)[0];return Object.keys(n).length>0?this.queries.filter((function(t){return v(n,t)})):this.queries},n.notify=function(t){var e=this;A.batch((function(){e.listeners.forEach((function(e){e(t)}))}))},n.onFocus=function(){var t=this;A.batch((function(){t.queries.forEach((function(t){t.onFocus()}))}))},n.onOnline=function(){var t=this;A.batch((function(){t.queries.forEach((function(t){t.onOnline()}))}))},e}(i),K=function(){function t(t){this.options=u({},t.defaultOptions,t.options),this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.observers=[],this.state=t.state||L(),this.meta=t.meta}var e=t.prototype;return e.setState=function(t){this.dispatch({type:"setState",state:t})},e.addObserver=function(t){-1===this.observers.indexOf(t)&&this.observers.push(t)},e.removeObserver=function(t){this.observers=this.observers.filter((function(e){return e!==t}))},e.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(o).catch(o)):Promise.resolve()},e.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},e.execute=function(){var t,e=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then((function(){null==e.mutationCache.config.onMutate||e.mutationCache.config.onMutate(e.state.variables,e)})).then((function(){return null==e.options.onMutate?void 0:e.options.onMutate(e.state.variables)})).then((function(t){t!==e.state.context&&e.dispatch({type:"loading",context:t,variables:e.state.variables})}))),r.then((function(){return e.executeMutation()})).then((function(n){t=n,null==e.mutationCache.config.onSuccess||e.mutationCache.config.onSuccess(t,e.state.variables,e.state.context,e)})).then((function(){return null==e.options.onSuccess?void 0:e.options.onSuccess(t,e.state.variables,e.state.context)})).then((function(){return null==e.options.onSettled?void 0:e.options.onSettled(t,null,e.state.variables,e.state.context)})).then((function(){return e.dispatch({type:"success",data:t}),t})).catch((function(t){return null==e.mutationCache.config.onError||e.mutationCache.config.onError(t,e.state.variables,e.state.context,e),U().error(t),Promise.resolve().then((function(){return null==e.options.onError?void 0:e.options.onError(t,e.state.variables,e.state.context)})).then((function(){return null==e.options.onSettled?void 0:e.options.onSettled(void 0,t,e.state.variables,e.state.context)})).then((function(){throw e.dispatch({type:"error",error:t}),t}))}))},e.executeMutation=function(){var t,e=this;return this.retryer=new x({fn:function(){return e.options.mutationFn?e.options.mutationFn(e.state.variables):Promise.reject("No mutationFn found")},onFail:function(){e.dispatch({type:"failed"})},onPause:function(){e.dispatch({type:"pause"})},onContinue:function(){e.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay}),this.retryer.promise},e.dispatch=function(t){var e=this;this.state=function(t,e){switch(e.type){case"failed":return u({},t,{failureCount:t.failureCount+1});case"pause":return u({},t,{isPaused:!0});case"continue":return u({},t,{isPaused:!1});case"loading":return u({},t,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return u({},t,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return u({},t,{data:void 0,error:e.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return u({},t,e.state);default:return t}}(this.state,t),A.batch((function(){e.observers.forEach((function(e){e.onMutationUpdate(t)})),e.mutationCache.notify(e)}))},t}();function L(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}var j=function(t){function e(e){var n;return(n=t.call(this)||this).config=e||{},n.mutations=[],n.mutationId=0,n}r(e,t);var n=e.prototype;return n.build=function(t,e,n){var r=new K({mutationCache:this,mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:n,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0,meta:e.meta});return this.add(r),r},n.add=function(t){this.mutations.push(t),this.notify(t)},n.remove=function(t){this.mutations=this.mutations.filter((function(e){return e!==t})),t.cancel(),this.notify(t)},n.clear=function(){var t=this;A.batch((function(){t.mutations.forEach((function(e){t.remove(e)}))}))},n.getAll=function(){return this.mutations},n.find=function(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find((function(e){return p(t,e)}))},n.findAll=function(t){return this.mutations.filter((function(e){return p(t,e)}))},n.notify=function(t){var e=this;A.batch((function(){e.listeners.forEach((function(e){e(t)}))}))},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var t=this.mutations.filter((function(t){return t.state.isPaused}));return A.batch((function(){return t.reduce((function(t,e){return t.then((function(){return e.continue().catch(o)}))}),Promise.resolve())}))},e}(i);function H(){return{onFetch:function(t){t.fetchFn=function(){var e,n,r,i,u,s,o,a=null==(e=t.fetchOptions)||null==(n=e.meta)?void 0:n.refetchPage,c=null==(r=t.fetchOptions)||null==(i=r.meta)?void 0:i.fetchMore,l=null==c?void 0:c.pageParam,h="forward"===(null==c?void 0:c.direction),f="backward"===(null==c?void 0:c.direction),d=(null==(u=t.state.data)?void 0:u.pages)||[],v=(null==(s=t.state.data)?void 0:s.pageParams)||[],p=q(),y=null==p?void 0:p.signal,m=v,b=!1,g=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},O=function(t,e,n,r){return m=r?[e].concat(m):[].concat(m,[e]),r?[n].concat(t):[].concat(t,[n])},C=function(e,n,r,i){if(b)return Promise.reject("Cancelled");if(void 0===r&&!n&&e.length)return Promise.resolve(e);var u={queryKey:t.queryKey,signal:y,pageParam:r,meta:t.meta},s=g(u),o=Promise.resolve(s).then((function(t){return O(e,r,t,i)}));F(s)&&(o.cancel=s.cancel);return o};if(d.length)if(h){var R=void 0!==l,P=R?l:k(t.options,d);o=C(d,R,P)}else if(f){var Q=void 0!==l,E=Q?l:N(t.options,d);o=C(d,Q,E,!0)}else!function(){m=[];var e=void 0===t.options.getNextPageParam,n=!a||!d[0]||a(d[0],0,d);o=n?C([],e,v[0]):Promise.resolve(O([],v[0],d[0]));for(var r=function(n){o=o.then((function(r){if(!a||!d[n]||a(d[n],n,d)){var i=e?v[n]:k(t.options,r);return C(r,e,i)}return Promise.resolve(O(r,v[n],d[n]))}))},i=1;i<d.length;i++)r(i)}();else o=C([]);var S=o.then((function(t){return{pages:t,pageParams:m}}));return S.cancel=function(){b=!0,null==p||p.abort(),F(o)&&o.cancel()},S}}}}function k(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}function N(t,e){return null==t.getPreviousPageParam?void 0:t.getPreviousPageParam(e[0],e)}function B(t,e){if(t.getNextPageParam&&Array.isArray(e)){var n=k(t,e);return null!=n&&!1!==n}}function _(t,e){if(t.getPreviousPageParam&&Array.isArray(e)){var n=N(t,e);return null!=n&&!1!==n}}var G=function(){function t(t){void 0===t&&(t={}),this.queryCache=t.queryCache||new T,this.mutationCache=t.mutationCache||new j,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var e=t.prototype;return e.mount=function(){var t=this;this.unsubscribeFocus=Q.subscribe((function(){Q.isFocused()&&E.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())})),this.unsubscribeOnline=E.subscribe((function(){Q.isFocused()&&E.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())}))},e.unmount=function(){var t,e;null==(t=this.unsubscribeFocus)||t.call(this),null==(e=this.unsubscribeOnline)||e.call(this)},e.isFetching=function(t,e){var n=d(t,e)[0];return n.fetching=!0,this.queryCache.findAll(n).length},e.isMutating=function(t){return this.mutationCache.findAll(u({},t,{fetching:!0})).length},e.getQueryData=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state.data},e.getQueriesData=function(t){return this.getQueryCache().findAll(t).map((function(t){return[t.queryKey,t.state.data]}))},e.setQueryData=function(t,e,n){var r=f(t),i=this.defaultQueryOptions(r);return this.queryCache.build(this,i).setData(e,n)},e.setQueriesData=function(t,e,n){var r=this;return A.batch((function(){return r.getQueryCache().findAll(t).map((function(t){var i=t.queryKey;return[i,r.setQueryData(i,e,n)]}))}))},e.getQueryState=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state},e.removeQueries=function(t,e){var n=d(t,e)[0],r=this.queryCache;A.batch((function(){r.findAll(n).forEach((function(t){r.remove(t)}))}))},e.resetQueries=function(t,e,n){var r=this,i=d(t,e,n),s=i[0],o=i[1],a=this.queryCache,c=u({},s,{active:!0});return A.batch((function(){return a.findAll(s).forEach((function(t){t.reset()})),r.refetchQueries(c,o)}))},e.cancelQueries=function(t,e,n){var r=this,i=d(t,e,n),u=i[0],s=i[1],a=void 0===s?{}:s;void 0===a.revert&&(a.revert=!0);var c=A.batch((function(){return r.queryCache.findAll(u).map((function(t){return t.cancel(a)}))}));return Promise.all(c).then(o).catch(o)},e.invalidateQueries=function(t,e,n){var r,i,s,o=this,a=d(t,e,n),c=a[0],l=a[1],h=u({},c,{active:null==(r=null!=(i=c.refetchActive)?i:c.active)||r,inactive:null!=(s=c.refetchInactive)&&s});return A.batch((function(){return o.queryCache.findAll(c).forEach((function(t){t.invalidate()})),o.refetchQueries(h,l)}))},e.refetchQueries=function(t,e,n){var r=this,i=d(t,e,n),s=i[0],a=i[1],c=A.batch((function(){return r.queryCache.findAll(s).map((function(t){return t.fetch(void 0,u({},a,{meta:{refetchPage:null==s?void 0:s.refetchPage}}))}))})),l=Promise.all(c).then(o);return(null==a?void 0:a.throwOnError)||(l=l.catch(o)),l},e.fetchQuery=function(t,e,n){var r=f(t,e,n),i=this.defaultQueryOptions(r);void 0===i.retry&&(i.retry=!1);var u=this.queryCache.build(this,i);return u.isStaleByTime(i.staleTime)?u.fetch(i):Promise.resolve(u.state.data)},e.prefetchQuery=function(t,e,n){return this.fetchQuery(t,e,n).then(o).catch(o)},e.fetchInfiniteQuery=function(t,e,n){var r=f(t,e,n);return r.behavior=H(),this.fetchQuery(r)},e.prefetchInfiniteQuery=function(t,e,n){return this.fetchInfiniteQuery(t,e,n).then(o).catch(o)},e.cancelMutations=function(){var t=this,e=A.batch((function(){return t.mutationCache.getAll().map((function(t){return t.cancel()}))}));return Promise.all(e).then(o).catch(o)},e.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},e.executeMutation=function(t){return this.mutationCache.build(this,t).execute()},e.getQueryCache=function(){return this.queryCache},e.getMutationCache=function(){return this.mutationCache},e.getDefaultOptions=function(){return this.defaultOptions},e.setDefaultOptions=function(t){this.defaultOptions=t},e.setQueryDefaults=function(t,e){var n=this.queryDefaults.find((function(e){return m(t)===m(e.queryKey)}));n?n.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})},e.getQueryDefaults=function(t){var e;return t?null==(e=this.queryDefaults.find((function(e){return b(t,e.queryKey)})))?void 0:e.defaultOptions:void 0},e.setMutationDefaults=function(t,e){var n=this.mutationDefaults.find((function(e){return m(t)===m(e.mutationKey)}));n?n.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})},e.getMutationDefaults=function(t){var e;return t?null==(e=this.mutationDefaults.find((function(e){return b(t,e.mutationKey)})))?void 0:e.defaultOptions:void 0},e.defaultQueryOptions=function(t){if(null==t?void 0:t._defaulted)return t;var e=u({},this.defaultOptions.queries,this.getQueryDefaults(null==t?void 0:t.queryKey),t,{_defaulted:!0});return!e.queryHash&&e.queryKey&&(e.queryHash=y(e.queryKey,e)),e},e.defaultQueryObserverOptions=function(t){return this.defaultQueryOptions(t)},e.defaultMutationOptions=function(t){return(null==t?void 0:t._defaulted)?t:u({},this.defaultOptions.mutations,this.getMutationDefaults(null==t?void 0:t.mutationKey),t,{_defaulted:!0})},e.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},t}(),W=function(t){function e(e,n){var r;return(r=t.call(this)||this).client=e,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}r(e,t);var n=e.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),J(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return z(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return z(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(t,e){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var i=this.hasListeners();i&&V(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(e),!i||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var u=this.computeRefetchInterval();!i||this.currentQuery===r&&this.options.enabled===n.enabled&&u===this.currentRefetchInterval||this.updateRefetchInterval(u)},n.getOptimisticResult=function(t){var e=this.client.defaultQueryObserverOptions(t),n=this.client.getQueryCache().build(this.client,e);return this.createResult(n,e)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(t,e){var n=this,r={},i=function(t){n.trackedProps.includes(t)||n.trackedProps.push(t)};return Object.keys(t).forEach((function(e){Object.defineProperty(r,e,{configurable:!1,enumerable:!0,get:function(){return i(e),t[e]}})})),(e.useErrorBoundary||e.suspense)&&i("error"),r},n.getNextResult=function(t){var e=this;return new Promise((function(n,r){var i=e.subscribe((function(e){e.isFetching||(i(),e.isError&&(null==t?void 0:t.throwOnError)?r(e.error):n(e))}))}))},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(t){return this.fetch(u({},t,{meta:{refetchPage:null==t?void 0:t.refetchPage}}))},n.fetchOptimistic=function(t){var e=this,n=this.client.defaultQueryObserverOptions(t),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then((function(){return e.createResult(r,n)}))},n.fetch=function(t){var e=this;return this.executeFetch(t).then((function(){return e.updateResult(),e.currentResult}))},n.executeFetch=function(t){this.updateQuery();var e=this.currentQuery.fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(o)),e},n.updateStaleTimeout=function(){var t=this;if(this.clearStaleTimeout(),!s&&!this.currentResult.isStale&&a(this.options.staleTime)){var e=h(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){t.currentResult.isStale||t.updateResult()}),e)}},n.computeRefetchInterval=function(){var t;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t},n.updateRefetchInterval=function(t){var e=this;this.clearRefetchInterval(),this.currentRefetchInterval=t,!s&&!1!==this.options.enabled&&a(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(e.options.refetchIntervalInBackground||Q.isFocused())&&e.executeFetch()}),this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(t,e){var n,r=this.currentQuery,i=this.options,u=this.currentResult,s=this.currentResultState,o=this.currentResultOptions,a=t!==r,c=a?t.state:this.currentQueryInitialState,l=a?this.currentResult:this.previousQueryResult,h=t.state,f=h.dataUpdatedAt,d=h.error,v=h.errorUpdatedAt,p=h.isFetching,y=h.status,m=!1,b=!1;if(e.optimisticResults){var O=this.hasListeners(),C=!O&&J(t,e),R=O&&V(t,r,e,i);(C||R)&&(p=!0,f||(y="loading"))}if(e.keepPreviousData&&!h.dataUpdateCount&&(null==l?void 0:l.isSuccess)&&"error"!==y)n=l.data,f=l.dataUpdatedAt,y=l.status,m=!0;else if(e.select&&void 0!==h.data)if(u&&h.data===(null==s?void 0:s.data)&&e.select===this.selectFn)n=this.selectResult;else try{this.selectFn=e.select,n=e.select(h.data),!1!==e.structuralSharing&&(n=g(null==u?void 0:u.data,n)),this.selectResult=n,this.selectError=null}catch(t){U().error(t),this.selectError=t}else n=h.data;if(void 0!==e.placeholderData&&void 0===n&&("loading"===y||"idle"===y)){var P;if((null==u?void 0:u.isPlaceholderData)&&e.placeholderData===(null==o?void 0:o.placeholderData))P=u.data;else if(P="function"==typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&void 0!==P)try{P=e.select(P),!1!==e.structuralSharing&&(P=g(null==u?void 0:u.data,P)),this.selectError=null}catch(t){U().error(t),this.selectError=t}void 0!==P&&(y="success",n=P,b=!0)}return this.selectError&&(d=this.selectError,n=this.selectResult,v=Date.now(),y="error"),{status:y,isLoading:"loading"===y,isSuccess:"success"===y,isError:"error"===y,isIdle:"idle"===y,data:n,dataUpdatedAt:f,error:d,errorUpdatedAt:v,failureCount:h.fetchFailureCount,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>c.dataUpdateCount||h.errorUpdateCount>c.errorUpdateCount,isFetching:p,isRefetching:p&&"loading"!==y,isLoadingError:"error"===y&&0===h.dataUpdatedAt,isPlaceholderData:b,isPreviousData:m,isRefetchError:"error"===y&&0!==h.dataUpdatedAt,isStale:X(t,e),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(t,e){if(!e)return!0;var n=this.options,r=n.notifyOnChangeProps,i=n.notifyOnChangePropsExclusions;if(!r&&!i)return!0;if("tracked"===r&&!this.trackedProps.length)return!0;var u="tracked"===r?this.trackedProps:r;return Object.keys(t).some((function(n){var r=n,s=t[r]!==e[r],o=null==u?void 0:u.some((function(t){return t===n})),a=null==i?void 0:i.some((function(t){return t===n}));return s&&!a&&(!u||o)}))},n.updateResult=function(t){var e=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!function(t,e){if(t&&!e||e&&!t)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}(this.currentResult,e)){var n={cache:!0};!1!==(null==t?void 0:t.listeners)&&this.shouldNotifyListeners(this.currentResult,e)&&(n.listeners=!0),this.notify(u({},n,t))}},n.updateQuery=function(){var t=this.client.getQueryCache().build(this.client,this.options);if(t!==this.currentQuery){var e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}},n.onQueryUpdate=function(t){var e={};"success"===t.type?e.onSuccess=!0:"error"!==t.type||w(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()},n.notify=function(t){var e=this;A.batch((function(){t.onSuccess?(null==e.options.onSuccess||e.options.onSuccess(e.currentResult.data),null==e.options.onSettled||e.options.onSettled(e.currentResult.data,null)):t.onError&&(null==e.options.onError||e.options.onError(e.currentResult.error),null==e.options.onSettled||e.options.onSettled(void 0,e.currentResult.error)),t.listeners&&e.listeners.forEach((function(t){t(e.currentResult)})),t.cache&&e.client.getQueryCache().notify({query:e.currentQuery,type:"observerResultsUpdated"})}))},e}(i);function J(t,e){return function(t,e){return!(!1===e.enabled||t.state.dataUpdatedAt||"error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&z(t,e,e.refetchOnMount)}function z(t,e,n){if(!1!==e.enabled){var r="function"==typeof n?n(t):n;return"always"===r||!1!==r&&X(t,e)}return!1}function V(t,e,n,r){return!1!==n.enabled&&(t!==e||!1===r.enabled)&&(!n.suspense||"error"!==t.state.status)&&X(t,n)}function X(t,e){return t.isStaleByTime(e.staleTime)}var Y=function(t){function e(e,n){var r;return(r=t.call(this)||this).client=e,r.queries=[],r.result=[],r.observers=[],r.observersMap={},n&&r.setQueries(n),r}r(e,t);var n=e.prototype;return n.onSubscribe=function(){var t=this;1===this.listeners.length&&this.observers.forEach((function(e){e.subscribe((function(n){t.onUpdate(e,n)}))}))},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.destroy=function(){this.listeners=[],this.observers.forEach((function(t){t.destroy()}))},n.setQueries=function(t,e){this.queries=t,this.updateObservers(e)},n.getCurrentResult=function(){return this.result},n.getOptimisticResult=function(t){return this.findMatchingObservers(t).map((function(t){return t.observer.getOptimisticResult(t.defaultedQueryOptions)}))},n.findMatchingObservers=function(t){var e=this,n=this.observers,r=t.map((function(t){return e.client.defaultQueryObserverOptions(t)})),i=r.flatMap((function(t){var e=n.find((function(e){return e.options.queryHash===t.queryHash}));return null!=e?[{defaultedQueryOptions:t,observer:e}]:[]})),u=i.map((function(t){return t.defaultedQueryOptions.queryHash})),s=r.filter((function(t){return!u.includes(t.queryHash)})),o=n.filter((function(t){return!i.some((function(e){return e.observer===t}))})),a=s.map((function(t,n){if(t.keepPreviousData){var r=o[n];if(void 0!==r)return{defaultedQueryOptions:t,observer:r}}return{defaultedQueryOptions:t,observer:e.getObserver(t)}}));return i.concat(a).sort((function(t,e){return r.indexOf(t.defaultedQueryOptions)-r.indexOf(e.defaultedQueryOptions)}))},n.getObserver=function(t){var e=this.client.defaultQueryObserverOptions(t),n=this.observersMap[e.queryHash];return null!=n?n:new W(this.client,e)},n.updateObservers=function(t){var e=this;A.batch((function(){var n=e.observers,r=e.findMatchingObservers(e.queries);r.forEach((function(e){return e.observer.setOptions(e.defaultedQueryOptions,t)}));var i=r.map((function(t){return t.observer})),u=Object.fromEntries(i.map((function(t){return[t.options.queryHash,t]}))),s=i.map((function(t){return t.getCurrentResult()})),o=i.some((function(t,e){return t!==n[e]}));(n.length!==i.length||o)&&(e.observers=i,e.observersMap=u,e.result=s,e.hasListeners()&&(l(n,i).forEach((function(t){t.destroy()})),l(i,n).forEach((function(t){t.subscribe((function(n){e.onUpdate(t,n)}))})),e.notify()))}))},n.onUpdate=function(t,e){var n=this.observers.indexOf(t);-1!==n&&(this.result=function(t,e,n){var r=t.slice(0);return r[e]=n,r}(this.result,n,e),this.notify())},n.notify=function(){var t=this;A.batch((function(){t.listeners.forEach((function(e){e(t.result)}))}))},e}(i),Z=function(t){function e(e,n){return t.call(this,e,n)||this}r(e,t);var n=e.prototype;return n.bindMethods=function(){t.prototype.bindMethods.call(this),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)},n.setOptions=function(e,n){t.prototype.setOptions.call(this,u({},e,{behavior:H()}),n)},n.getOptimisticResult=function(e){return e.behavior=H(),t.prototype.getOptimisticResult.call(this,e)},n.fetchNextPage=function(t){var e;return this.fetch({cancelRefetch:null==(e=null==t?void 0:t.cancelRefetch)||e,throwOnError:null==t?void 0:t.throwOnError,meta:{fetchMore:{direction:"forward",pageParam:null==t?void 0:t.pageParam}}})},n.fetchPreviousPage=function(t){var e;return this.fetch({cancelRefetch:null==(e=null==t?void 0:t.cancelRefetch)||e,throwOnError:null==t?void 0:t.throwOnError,meta:{fetchMore:{direction:"backward",pageParam:null==t?void 0:t.pageParam}}})},n.createResult=function(e,n){var r,i,s,o,a,c,l=e.state;return u({},t.prototype.createResult.call(this,e,n),{fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:B(n,null==(r=l.data)?void 0:r.pages),hasPreviousPage:_(n,null==(i=l.data)?void 0:i.pages),isFetchingNextPage:l.isFetching&&"forward"===(null==(s=l.fetchMeta)||null==(o=s.fetchMore)?void 0:o.direction),isFetchingPreviousPage:l.isFetching&&"backward"===(null==(a=l.fetchMeta)||null==(c=a.fetchMore)?void 0:c.direction)})},e}(W),$=function(t){function e(e,n){var r;return(r=t.call(this)||this).client=e,r.setOptions(n),r.bindMethods(),r.updateResult(),r}r(e,t);var n=e.prototype;return n.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},n.setOptions=function(t){this.options=this.client.defaultMutationOptions(t)},n.onUnsubscribe=function(){var t;this.listeners.length||(null==(t=this.currentMutation)||t.removeObserver(this))},n.onMutationUpdate=function(t){this.updateResult();var e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)},n.getCurrentResult=function(){return this.currentResult},n.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},n.mutate=function(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,u({},this.options,{variables:void 0!==t?t:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},n.updateResult=function(){var t=this.currentMutation?this.currentMutation.state:{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},e=u({},t,{isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset});this.currentResult=e},n.notify=function(t){var e=this;A.batch((function(){e.mutateOptions&&(t.onSuccess?(null==e.mutateOptions.onSuccess||e.mutateOptions.onSuccess(e.currentResult.data,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(e.currentResult.data,null,e.currentResult.variables,e.currentResult.context)):t.onError&&(null==e.mutateOptions.onError||e.mutateOptions.onError(e.currentResult.error,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(void 0,e.currentResult.error,e.currentResult.variables,e.currentResult.context))),t.listeners&&e.listeners.forEach((function(t){t(e.currentResult)}))}))},e}(i);function tt(t){return t.state.isPaused}function et(t){return"success"===t.state.status}function nt(t,e,n){if("object"==typeof e&&null!==e){var r=t.getMutationCache(),i=t.getQueryCache(),s=e.mutations||[],o=e.queries||[];s.forEach((function(e){var i;r.build(t,u({},null==n||null==(i=n.defaultOptions)?void 0:i.mutations,{mutationKey:e.mutationKey}),e.state)})),o.forEach((function(e){var r,s=i.get(e.queryHash);s?s.state.dataUpdatedAt<e.state.dataUpdatedAt&&s.setState(e.state):i.build(t,u({},null==n||null==(r=n.defaultOptions)?void 0:r.queries,{queryKey:e.queryKey,queryHash:e.queryHash}),e.state)}))}}var rt=n.createContext(void 0),it=n.createContext(!1);function ut(t){return t&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=rt),window.ReactQueryClientContext):rt}var st=function(){var t=n.useContext(ut(n.useContext(it)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t};function ot(){var t=!1;return{clearReset:function(){t=!1},reset:function(){t=!0},isReset:function(){return t}}}var at=n.createContext(ot()),ct=function(){return n.useContext(at)},lt=function(t,e,n,r){var i=t.isFetching(e);n!==i&&r(i)};function ht(t,e,n){return"function"==typeof e?e.apply(void 0,n):"boolean"==typeof e?e:!!t}function ft(t,e){var r=n.useRef(!1),i=n.useState(0)[1],u=st(),s=ct(),o=u.defaultQueryObserverOptions(t);o.optimisticResults=!0,o.onError&&(o.onError=A.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=A.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=A.batchCalls(o.onSettled)),o.suspense&&("number"!=typeof o.staleTime&&(o.staleTime=1e3),0===o.cacheTime&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(s.isReset()||(o.retryOnMount=!1));var a=n.useState((function(){return new e(u,o)}))[0],c=a.getOptimisticResult(o);if(n.useEffect((function(){r.current=!0,s.clearReset();var t=a.subscribe(A.batchCalls((function(){r.current&&i((function(t){return t+1}))})));return a.updateResult(),function(){r.current=!1,t()}}),[s,a]),n.useEffect((function(){a.setOptions(o,{listeners:!1})}),[o,a]),o.suspense&&c.isLoading)throw a.fetchOptimistic(o).then((function(t){var e=t.data;null==o.onSuccess||o.onSuccess(e),null==o.onSettled||o.onSettled(e,null)})).catch((function(t){s.clearReset(),null==o.onError||o.onError(t),null==o.onSettled||o.onSettled(void 0,t)}));if(c.isError&&!s.isReset()&&!c.isFetching&&ht(o.suspense,o.useErrorBoundary,[c.error,a.getCurrentQuery()]))throw c.error;return"tracked"===o.notifyOnChangeProps&&(c=a.trackResult(c,o)),c}function dt(t,e){var r=st(),i=n.useRef(e);i.current=e,n.useMemo((function(){t&&nt(r,t,i.current)}),[r,t])}t.CancelledError=M,t.Hydrate=function(t){var e=t.children,n=t.options;return dt(t.state,n),e},t.InfiniteQueryObserver=Z,t.MutationCache=j,t.MutationObserver=$,t.QueriesObserver=Y,t.QueryCache=T,t.QueryClient=G,t.QueryClientProvider=function(t){var e=t.client,r=t.contextSharing,i=void 0!==r&&r,u=t.children;n.useEffect((function(){return e.mount(),function(){e.unmount()}}),[e]);var s=ut(i);return n.createElement(it.Provider,{value:i},n.createElement(s.Provider,{value:e},u))},t.QueryErrorResetBoundary=function(t){var e=t.children,r=n.useMemo((function(){return ot()}),[]);return n.createElement(at.Provider,{value:r},"function"==typeof e?e(r):e)},t.QueryObserver=W,t.dehydrate=function(t,e){var n,r,i=[],u=[];if(!1!==(null==(n=e=e||{})?void 0:n.dehydrateMutations)){var s=e.shouldDehydrateMutation||tt;t.getMutationCache().getAll().forEach((function(t){s(t)&&i.push(function(t){return{mutationKey:t.options.mutationKey,state:t.state}}(t))}))}if(!1!==(null==(r=e)?void 0:r.dehydrateQueries)){var o=e.shouldDehydrateQuery||et;t.getQueryCache().getAll().forEach((function(t){o(t)&&u.push(function(t){return{state:t.state,queryKey:t.queryKey,queryHash:t.queryHash}}(t))}))}return{mutations:i,queries:u}},t.focusManager=Q,t.hashQueryKey=m,t.hydrate=nt,t.isCancelledError=w,t.isError=function(t){return t instanceof Error},t.notifyManager=A,t.onlineManager=E,t.setLogger=function(t){D=t},t.useHydrate=dt,t.useInfiniteQuery=function(t,e,n){return ft(f(t,e,n),Z)},t.useIsFetching=function(t,e){var r=n.useRef(!1),i=st(),u=d(t,e)[0],s=n.useState(i.isFetching(u)),o=s[0],a=s[1],c=n.useRef(u);c.current=u;var l=n.useRef(o);return l.current=o,n.useEffect((function(){r.current=!0,lt(i,c.current,l.current,a);var t=i.getQueryCache().subscribe(A.batchCalls((function(){r.current&&lt(i,c.current,l.current,a)})));return function(){r.current=!1,t()}}),[i]),o},t.useIsMutating=function(t,e){var r=n.useRef(!1),i=function(t,e){return R(t)?u({},e,{mutationKey:t}):t}(t,e),s=st(),o=n.useState(s.isMutating(i)),a=o[0],c=o[1],l=n.useRef(i);l.current=i;var h=n.useRef(a);return h.current=a,n.useEffect((function(){r.current=!0;var t=s.getMutationCache().subscribe(A.batchCalls((function(){if(r.current){var t=s.isMutating(l.current);h.current!==t&&c(t)}})));return function(){r.current=!1,t()}}),[s]),a},t.useMutation=function(t,e,r){var i=n.useRef(!1),s=n.useState(0)[1],a=function(t,e,n){return R(t)?"function"==typeof e?u({},n,{mutationKey:t,mutationFn:e}):u({},e,{mutationKey:t}):"function"==typeof t?u({},e,{mutationFn:t}):u({},t)}(t,e,r),c=st(),l=n.useRef();l.current?l.current.setOptions(a):l.current=new $(c,a);var h=l.current.getCurrentResult();n.useEffect((function(){i.current=!0;var t=l.current.subscribe(A.batchCalls((function(){i.current&&s((function(t){return t+1}))})));return function(){i.current=!1,t()}}),[]);var f=n.useCallback((function(t,e){l.current.mutate(t,e).catch(o)}),[]);if(h.error&&ht(void 0,l.current.options.useErrorBoundary,[h.error]))throw h.error;return u({},h,{mutate:f,mutateAsync:h.mutate})},t.useQueries=function(t){var r=n.useRef(!1),i=n.useState(0)[1],u=st(),s=e.useMemo((function(){return t.map((function(t){var e=u.defaultQueryObserverOptions(t);return e.optimisticResults=!0,e}))}),[t,u]),o=n.useState((function(){return new Y(u,s)}))[0],a=o.getOptimisticResult(s);return n.useEffect((function(){r.current=!0;var t=o.subscribe(A.batchCalls((function(){r.current&&i((function(t){return t+1}))})));return function(){r.current=!1,t()}}),[o]),n.useEffect((function(){o.setQueries(s,{listeners:!1})}),[s,o]),a},t.useQuery=function(t,e,n){return ft(f(t,e,n),W)},t.useQueryClient=st,t.useQueryErrorResetBoundary=ct,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=react-query.production.min.js.map
