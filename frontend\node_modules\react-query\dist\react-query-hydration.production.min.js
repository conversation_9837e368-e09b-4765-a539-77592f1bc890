!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react-query")):"function"==typeof define&&define.amd?define(["exports","react-query"],t):t((e=e||self).ReactQueryHydration={},e.<PERSON>)}(this,(function(e,t){"use strict";Object.defineProperty(e,"Hydrate",{enumerable:!0,get:function(){return t.Hydrate}}),Object.defineProperty(e,"dehydrate",{enumerable:!0,get:function(){return t.dehydrate}}),Object.defineProperty(e,"hydrate",{enumerable:!0,get:function(){return t.hydrate}}),Object.defineProperty(e,"useHydrate",{enumerable:!0,get:function(){return t.useHydrate}}),Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-query-hydration.production.min.js.map
