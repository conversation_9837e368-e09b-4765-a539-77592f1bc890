import{_getProvider,getApp as t,_registerComponent as e,registerVersion as n}from"https://www.gstatic.com/firebasejs/10.14.1/firebase-app.js";class FirebaseError extends Error{constructor(t,e,n){super(e),this.code=t,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(t,e,n){this.service=t,this.serviceName=e,this.errors=n}create(t,...e){const n=e[0]||{},o=`${this.service}/${t}`,r=this.errors[t],i=r?function replaceTemplate(t,e){return t.replace(s,((t,n)=>{const s=e[n];return null!=s?String(s):`<${n}?>`}))}(r,n):"Error",a=`${this.serviceName}: ${i} (${o}).`;return new FirebaseError(o,a,n)}}const s=/\{\$([^}]+)}/g;class Component{constructor(t,e,n){this.name=t,this.instanceFactory=e,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}}function __await(t){return this instanceof __await?(this.v=t,this):new __await(t)}function __asyncGenerator(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,o=n.apply(t,e||[]),r=[];return s={},verb("next"),verb("throw"),verb("return"),s[Symbol.asyncIterator]=function(){return this},s;function verb(t){o[t]&&(s[t]=function(e){return new Promise((function(n,s){r.push([t,e,n,s])>1||resume(t,e)}))})}function resume(t,e){try{!function step(t){t.value instanceof __await?Promise.resolve(t.value.v).then(fulfill,reject):settle(r[0][2],t)}(o[t](e))}catch(t){settle(r[0][3],t)}}function fulfill(t){resume("next",t)}function reject(t){resume("throw",t)}function settle(t,e){t(e),r.shift(),r.length&&resume(r[0][0],r[0][1])}}var o="@firebase/vertexai-preview";class VertexAIService{constructor(t,e,n,s){var o;this.app=t,this.options=s;const r=null==n?void 0:n.getImmediate({optional:!0}),i=null==e?void 0:e.getImmediate({optional:!0});this.auth=i||null,this.appCheck=r||null,this.location=(null===(o=this.options)||void 0===o?void 0:o.location)||"us-central1"}_delete(){return Promise.resolve()}}class VertexAIError extends FirebaseError{constructor(t,e,n){const s=`vertexAI/${t}`,o=`VertexAI: ${e} (${s}).`;super(s,o),this.code=t,this.message=e,this.customErrorData=n,Error.captureStackTrace&&Error.captureStackTrace(this,VertexAIError),Object.setPrototypeOf(this,VertexAIError.prototype),this.toString=()=>o}}var r;!function(t){t.GENERATE_CONTENT="generateContent",t.STREAM_GENERATE_CONTENT="streamGenerateContent",t.COUNT_TOKENS="countTokens"}(r||(r={}));class RequestUrl{constructor(t,e,n,s,o){this.model=t,this.task=e,this.apiSettings=n,this.stream=s,this.requestOptions=o}toString(){var t;let e=`${(null===(t=this.requestOptions)||void 0===t?void 0:t.baseUrl)||"https://firebaseml.googleapis.com"}/v2beta`;return e+=`/projects/${this.apiSettings.project}`,e+=`/locations/${this.apiSettings.location}`,e+=`/${this.model}`,e+=`:${this.task}`,this.stream&&(e+="?alt=sse"),e}get fullModelString(){let t=`projects/${this.apiSettings.project}`;return t+=`/locations/${this.apiSettings.location}`,t+=`/${this.model}`,t}}async function getHeaders(t){const e=new Headers;if(e.append("Content-Type","application/json"),e.append("x-goog-api-client",function getClientHeaders(){const t=[];return t.push("gl-js/0.0.4"),t.push("fire/0.0.4"),t.join(" ")}()),e.append("x-goog-api-key",t.apiSettings.apiKey),t.apiSettings.getAppCheckToken){const n=await t.apiSettings.getAppCheckToken();n&&!n.error&&e.append("X-Firebase-AppCheck",n.token)}if(t.apiSettings.getAuthToken){const n=await t.apiSettings.getAuthToken();n&&e.append("Authorization",`Firebase ${n.accessToken}`)}return e}async function makeRequest(t,e,n,s,o,r){const i=new RequestUrl(t,e,n,s,r);let a;try{const c=await async function constructRequest(t,e,n,s,o,r){const i=new RequestUrl(t,e,n,s,r);return{url:i.toString(),fetchOptions:Object.assign(Object.assign({},buildFetchOptions(r)),{method:"POST",headers:await getHeaders(i),body:o})}}(t,e,n,s,o,r);if(a=await fetch(c.url,c.fetchOptions),!a.ok){let t,e="";try{const n=await a.json();e=n.error.message,n.error.details&&(e+=` ${JSON.stringify(n.error.details)}`,t=n.error.details)}catch(t){}throw new VertexAIError("fetch-error",`Error fetching from ${i}: [${a.status} ${a.statusText}] ${e}`,{status:a.status,statusText:a.statusText,errorDetails:t})}}catch(t){let e=t;throw"fetch-error"!==t.code&&t instanceof Error&&(e=new VertexAIError("error",`Error fetching from ${i.toString()}: ${t.message}`),e.stack=t.stack),e}return a}function buildFetchOptions(t){const e={};if((null==t?void 0:t.timeout)&&(null==t?void 0:t.timeout)>=0){const n=new AbortController,s=n.signal;setTimeout((()=>n.abort()),t.timeout),e.signal=s}return e}const i=["user","model","function","system"];var a,c,l,d,u,h,p,f,E;function addHelpers(t){return t.text=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),hadBadFinishReason(t.candidates[0]))throw new VertexAIError("response-error",`Response error: ${formatBlockErrorMessage(t)}. Response body stored in error.response`,{response:t});return function getText(t){var e,n,s,o;const r=[];if(null===(n=null===(e=t.candidates)||void 0===e?void 0:e[0].content)||void 0===n?void 0:n.parts)for(const e of null===(o=null===(s=t.candidates)||void 0===s?void 0:s[0].content)||void 0===o?void 0:o.parts)e.text&&r.push(e.text);return r.length>0?r.join(""):""}(t)}if(t.promptFeedback)throw new VertexAIError("response-error",`Text not available. ${formatBlockErrorMessage(t)}`,{response:t});return""},t.functionCalls=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),hadBadFinishReason(t.candidates[0]))throw new VertexAIError("response-error",`Response error: ${formatBlockErrorMessage(t)}. Response body stored in error.response`,{response:t});return function getFunctionCalls(t){var e,n,s,o;const r=[];if(null===(n=null===(e=t.candidates)||void 0===e?void 0:e[0].content)||void 0===n?void 0:n.parts)for(const e of null===(o=null===(s=t.candidates)||void 0===s?void 0:s[0].content)||void 0===o?void 0:o.parts)e.functionCall&&r.push(e.functionCall);return r.length>0?r:void 0}(t)}if(t.promptFeedback)throw new VertexAIError("response-error",`Function call not available. ${formatBlockErrorMessage(t)}`,{response:t})},t}!function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT"}(a||(a={})),function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"}(c||(c={})),function(t){t.HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",t.SEVERITY="SEVERITY",t.PROBABILITY="PROBABILITY"}(l||(l={})),function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"}(d||(d={})),function(t){t.HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",t.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",t.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",t.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",t.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH"}(u||(u={})),function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER"}(h||(h={})),function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.OTHER="OTHER"}(p||(p={})),function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"}(f||(f={})),function(t){t.STRING="STRING",t.NUMBER="NUMBER",t.INTEGER="INTEGER",t.BOOLEAN="BOOLEAN",t.ARRAY="ARRAY",t.OBJECT="OBJECT"}(E||(E={}));const g=[p.RECITATION,p.SAFETY];function hadBadFinishReason(t){return!!t.finishReason&&g.includes(t.finishReason)}function formatBlockErrorMessage(t){var e,n,s;let o="";if(t.candidates&&0!==t.candidates.length||!t.promptFeedback){if(null===(s=t.candidates)||void 0===s?void 0:s[0]){const e=t.candidates[0];hadBadFinishReason(e)&&(o+=`Candidate was blocked due to ${e.finishReason}`,e.finishMessage&&(o+=`: ${e.finishMessage}`))}}else o+="Response was blocked",(null===(e=t.promptFeedback)||void 0===e?void 0:e.blockReason)&&(o+=` due to ${t.promptFeedback.blockReason}`),(null===(n=t.promptFeedback)||void 0===n?void 0:n.blockReasonMessage)&&(o+=`: ${t.promptFeedback.blockReasonMessage}`);return o}const m=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function processStream(t){const e=function getResponseStream(t){const e=t.getReader();return new ReadableStream({start(t){let n="";return pump();function pump(){return e.read().then((({value:e,done:s})=>{if(s)return n.trim()?void t.error(new VertexAIError("parse-failed","Failed to parse stream")):void t.close();n+=e;let o,r=n.match(m);for(;r;){try{o=JSON.parse(r[1])}catch(e){return void t.error(new VertexAIError("parse-failed",`Error parsing JSON response: "${r[1]}`))}t.enqueue(o),n=n.substring(r[0].length),r=n.match(m)}return pump()}))}}})}(t.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))),[n,s]=e.tee();return{stream:generateResponseSequence(n),response:getResponsePromise(s)}}async function getResponsePromise(t){const e=[],n=t.getReader();for(;;){const{done:t,value:s}=await n.read();if(t)return addHelpers(aggregateResponses(e));e.push(s)}}function generateResponseSequence(t){return __asyncGenerator(this,arguments,(function*generateResponseSequence_1(){const e=t.getReader();for(;;){const{value:t,done:n}=yield __await(e.read());if(n)break;yield yield __await(addHelpers(t))}}))}function aggregateResponses(t){const e=t[t.length-1],n={promptFeedback:null==e?void 0:e.promptFeedback};for(const e of t)if(e.candidates)for(const t of e.candidates){const e=t.index;if(n.candidates||(n.candidates=[]),n.candidates[e]||(n.candidates[e]={index:t.index}),n.candidates[e].citationMetadata=t.citationMetadata,n.candidates[e].finishReason=t.finishReason,n.candidates[e].finishMessage=t.finishMessage,n.candidates[e].safetyRatings=t.safetyRatings,t.content&&t.content.parts){n.candidates[e].content||(n.candidates[e].content={role:t.content.role||"user",parts:[]});const s={};for(const o of t.content.parts)o.text&&(s.text=o.text),o.functionCall&&(s.functionCall=o.functionCall),0===Object.keys(s).length&&(s.text=""),n.candidates[e].content.parts.push(s)}}return n}async function generateContentStream(t,e,n,s){return processStream(await makeRequest(e,r.STREAM_GENERATE_CONTENT,t,!0,JSON.stringify(n),s))}async function generateContent(t,e,n,s){const o=await makeRequest(e,r.GENERATE_CONTENT,t,!1,JSON.stringify(n),s);return{response:addHelpers(await o.json())}}function formatSystemInstruction(t){if(null!=t)return"string"==typeof t?{role:"system",parts:[{text:t}]}:t.text?{role:"system",parts:[t]}:t.parts?t.role?t:{role:"system",parts:t.parts}:void 0}function formatNewContent(t){let e=[];if("string"==typeof t)e=[{text:t}];else for(const n of t)"string"==typeof n?e.push({text:n}):e.push(n);return function assignRoleToPartsAndValidateSendMessageRequest(t){const e={role:"user",parts:[]},n={role:"function",parts:[]};let s=!1,o=!1;for(const r of t)"functionResponse"in r?(n.parts.push(r),o=!0):(e.parts.push(r),s=!0);if(s&&o)throw new VertexAIError("invalid-content","Within a single message, FunctionResponse cannot be mixed with other type of Part in the request for sending chat message.");if(!s&&!o)throw new VertexAIError("invalid-content","No Content is provided for sending chat message.");if(s)return e;return n}(e)}function formatGenerateContentInput(t){let e;if(t.contents)e=t;else{e={contents:[formatNewContent(t)]}}return t.systemInstruction&&(e.systemInstruction=formatSystemInstruction(t.systemInstruction)),e}const I=["text","inlineData","functionCall","functionResponse"],_={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall"],system:["text"]},v={user:["model"],function:["model"],model:["user","function"],system:[]};class ChatSession{constructor(t,e,n,s){this.model=e,this.params=n,this.requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiSettings=t,(null==n?void 0:n.history)&&(!function validateChatHistory(t){let e=null;for(const n of t){const{role:t,parts:s}=n;if(!e&&"user"!==t)throw new VertexAIError("invalid-content",`First Content should be with role 'user', got ${t}`);if(!i.includes(t))throw new VertexAIError("invalid-content",`Each item should include role field. Got ${t} but valid roles are: ${JSON.stringify(i)}`);if(!Array.isArray(s))throw new VertexAIError("invalid-content","Content should have 'parts' but property with an array of Parts");if(0===s.length)throw new VertexAIError("invalid-content","Each Content should have at least one part");const o={text:0,inlineData:0,functionCall:0,functionResponse:0};for(const t of s)for(const e of I)e in t&&(o[e]+=1);const r=_[t];for(const e of I)if(!r.includes(e)&&o[e]>0)throw new VertexAIError("invalid-content",`Content with role '${t}' can't contain '${e}' part`);if(e&&!v[t].includes(e.role))throw new VertexAIError("invalid-content",`Content with role '${t} can't follow '${e.role}'. Valid previous roles: ${JSON.stringify(v)}`);e=n}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(t){var e,n,s,o,r;await this._sendPromise;const i=formatNewContent(t),a={safetySettings:null===(e=this.params)||void 0===e?void 0:e.safetySettings,generationConfig:null===(n=this.params)||void 0===n?void 0:n.generationConfig,tools:null===(s=this.params)||void 0===s?void 0:s.tools,toolConfig:null===(o=this.params)||void 0===o?void 0:o.toolConfig,systemInstruction:null===(r=this.params)||void 0===r?void 0:r.systemInstruction,contents:[...this._history,i]};let c={};return this._sendPromise=this._sendPromise.then((()=>generateContent(this._apiSettings,this.model,a,this.requestOptions))).then((t=>{var e,n;if(t.response.candidates&&t.response.candidates.length>0){this._history.push(i);const s={parts:(null===(e=t.response.candidates)||void 0===e?void 0:e[0].content.parts)||[],role:(null===(n=t.response.candidates)||void 0===n?void 0:n[0].content.role)||"model"};this._history.push(s)}else{const e=formatBlockErrorMessage(t.response);e&&console.warn(`sendMessage() was unsuccessful. ${e}. Inspect response object for details.`)}c=t})),await this._sendPromise,c}async sendMessageStream(t){var e,n,s,o,r;await this._sendPromise;const i=formatNewContent(t),a={safetySettings:null===(e=this.params)||void 0===e?void 0:e.safetySettings,generationConfig:null===(n=this.params)||void 0===n?void 0:n.generationConfig,tools:null===(s=this.params)||void 0===s?void 0:s.tools,toolConfig:null===(o=this.params)||void 0===o?void 0:o.toolConfig,systemInstruction:null===(r=this.params)||void 0===r?void 0:r.systemInstruction,contents:[...this._history,i]},c=generateContentStream(this._apiSettings,this.model,a,this.requestOptions);return this._sendPromise=this._sendPromise.then((()=>c)).catch((t=>{throw new Error("SILENT_ERROR")})).then((t=>t.response)).then((t=>{if(t.candidates&&t.candidates.length>0){this._history.push(i);const e=Object.assign({},t.candidates[0].content);e.role||(e.role="model"),this._history.push(e)}else{const e=formatBlockErrorMessage(t);e&&console.warn(`sendMessageStream() was unsuccessful. ${e}. Inspect response object for details.`)}})).catch((t=>{"SILENT_ERROR"!==t.message&&console.error(t)})),c}}class GenerativeModel{constructor(t,e,n){var s,o,r,i;if(!(null===(o=null===(s=t.app)||void 0===s?void 0:s.options)||void 0===o?void 0:o.apiKey))throw new VertexAIError("no-api-key",'The "apiKey" field is empty in the local Firebase config. Firebase VertexAI requires this field to contain a valid API key.');if(!(null===(i=null===(r=t.app)||void 0===r?void 0:r.options)||void 0===i?void 0:i.projectId))throw new VertexAIError("no-project-id",'The "projectId" field is empty in the local Firebase config. Firebase VertexAI requires this field to contain a valid project ID.');this._apiSettings={apiKey:t.app.options.apiKey,project:t.app.options.projectId,location:t.location},t.appCheck&&(this._apiSettings.getAppCheckToken=()=>t.appCheck.getToken()),t.auth&&(this._apiSettings.getAuthToken=()=>t.auth.getToken()),e.model.includes("/")?e.model.startsWith("models/")?this.model=`publishers/google/${e.model}`:this.model=e.model:this.model=`publishers/google/models/${e.model}`,this.generationConfig=e.generationConfig||{},this.safetySettings=e.safetySettings||[],this.tools=e.tools,this.toolConfig=e.toolConfig,this.systemInstruction=formatSystemInstruction(e.systemInstruction),this.requestOptions=n||{}}async generateContent(t){const e=formatGenerateContentInput(t);return generateContent(this._apiSettings,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction},e),this.requestOptions)}async generateContentStream(t){const e=formatGenerateContentInput(t);return generateContentStream(this._apiSettings,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction},e),this.requestOptions)}startChat(t){return new ChatSession(this._apiSettings,this.model,Object.assign({tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction},t),this.requestOptions)}async countTokens(t){const e=formatGenerateContentInput(t);return async function countTokens(t,e,n,s){return(await makeRequest(e,r.COUNT_TOKENS,t,!1,JSON.stringify(n),s)).json()}(this._apiSettings,this.model,e)}}function getVertexAI(e=t(),n){e=function getModularInstance(t){return t&&t._delegate?t._delegate:t}(e);return _getProvider(e,"vertexAI").getImmediate({identifier:(null==n?void 0:n.location)||"us-central1"})}function getGenerativeModel(t,e,n){if(!e.model)throw new VertexAIError("no-model","Must provide a model name. Example: getGenerativeModel({ model: 'my-model-name' })");return new GenerativeModel(t,e,n)}!function registerVertex(){e(new Component("vertexAI",((t,{instanceIdentifier:e})=>{const n=t.getProvider("app").getImmediate(),s=t.getProvider("auth-internal"),o=t.getProvider("app-check-internal");return new VertexAIService(n,s,o,{location:e})}),"PUBLIC").setMultipleInstances(!0)),n(o,"0.0.4"),n(o,"0.0.4","esm2017")}();export{h as BlockReason,ChatSession,p as FinishReason,f as FunctionCallingMode,E as FunctionDeclarationSchemaType,GenerativeModel,l as HarmBlockMethod,c as HarmBlockThreshold,a as HarmCategory,d as HarmProbability,u as HarmSeverity,i as POSSIBLE_ROLES,VertexAIError,getGenerativeModel,getVertexAI};

//# sourceMappingURL=firebase-vertexai-preview.js.map
