!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).ReactQueryCore={})}(this,(function(t){"use strict";function e(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var n=function(){function t(){this.listeners=[]}var e=t.prototype;return e.subscribe=function(t){var e=this,n=t||function(){};return this.listeners.push(n),this.onSubscribe(),function(){e.listeners=e.listeners.filter((function(t){return t!==n})),e.onUnsubscribe()}},e.hasListeners=function(){return this.listeners.length>0},e.onSubscribe=function(){},e.onUnsubscribe=function(){},t}();function i(){return(i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}var r="undefined"==typeof window;function s(){}function u(t){return"number"==typeof t&&t>=0&&t!==1/0}function o(t){return Array.isArray(t)?t:[t]}function a(t,e){return t.filter((function(t){return-1===e.indexOf(t)}))}function c(t,e){return Math.max(t+(e||0)-Date.now(),0)}function l(t,e,n){return O(t)?"function"==typeof e?i({},n,{queryKey:t,queryFn:e}):i({},e,{queryKey:t}):t}function h(t,e,n){return O(t)?[i({},e,{queryKey:t}),n]:[t||{},e]}function f(t,e){var n=t.active,i=t.exact,r=t.fetching,s=t.inactive,u=t.predicate,o=t.queryKey,a=t.stale;if(O(o))if(i){if(e.queryHash!==v(o,e.options))return!1}else if(!y(e.queryKey,o))return!1;var c=function(t,e){return!0===t&&!0===e||null==t&&null==e?"all":!1===t&&!1===e?"none":(null!=t?t:!e)?"active":"inactive"}(n,s);if("none"===c)return!1;if("all"!==c){var l=e.isActive();if("active"===c&&!l)return!1;if("inactive"===c&&l)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(("boolean"!=typeof r||e.isFetching()===r)&&!(u&&!u(e)))}function d(t,e){var n=t.exact,i=t.fetching,r=t.predicate,s=t.mutationKey;if(O(s)){if(!e.options.mutationKey)return!1;if(n){if(p(e.options.mutationKey)!==p(s))return!1}else if(!y(e.options.mutationKey,s))return!1}return("boolean"!=typeof i||"loading"===e.state.status===i)&&!(r&&!r(e))}function v(t,e){return((null==e?void 0:e.queryKeyHashFn)||p)(t)}function p(t){var e,n=o(t);return e=n,JSON.stringify(e,(function(t,e){return b(e)?Object.keys(e).sort().reduce((function(t,n){return t[n]=e[n],t}),{}):e}))}function y(t,e){return function t(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(e&&n&&"object"==typeof e&&"object"==typeof n)return!Object.keys(n).some((function(i){return!t(e[i],n[i])}));return!1}(o(t),o(e))}function m(t,e){if(t===e)return t;var n=Array.isArray(t)&&Array.isArray(e);if(n||b(t)&&b(e)){for(var i=n?t.length:Object.keys(t).length,r=n?e:Object.keys(e),s=r.length,u=n?[]:{},o=0,a=0;a<s;a++){var c=n?a:r[a];u[c]=m(t[c],e[c]),u[c]===t[c]&&o++}return i===s&&o===i?t:u}return e}function b(t){if(!g(t))return!1;var e=t.constructor;if(void 0===e)return!0;var n=e.prototype;return!!g(n)&&!!n.hasOwnProperty("isPrototypeOf")}function g(t){return"[object Object]"===Object.prototype.toString.call(t)}function O(t){return"string"==typeof t||Array.isArray(t)}function P(t){Promise.resolve().then(t).catch((function(t){return setTimeout((function(){throw t}))}))}function q(){if("function"==typeof AbortController)return new AbortController}var C=new(function(t){function n(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!r&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},e}e(n,t);var i=n.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},i.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t((function(t){"boolean"==typeof t?n.setFocused(t):n.onFocus()}))},i.setFocused=function(t){this.focused=t,t&&this.onFocus()},i.onFocus=function(){this.listeners.forEach((function(t){t()}))},i.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},n}(n)),R=new(function(t){function n(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!r&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},e}e(n,t);var i=n.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},i.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t((function(t){"boolean"==typeof t?n.setOnline(t):n.onOnline()}))},i.setOnline=function(t){this.online=t,t&&this.onOnline()},i.onOnline=function(){this.listeners.forEach((function(t){t()}))},i.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},n}(n));function Q(t){return Math.min(1e3*Math.pow(2,t),3e4)}function F(t){return"function"==typeof(null==t?void 0:t.cancel)}var E=function(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent};function S(t){return t instanceof E}var M=function(t){var e,n,i,r,s=this,u=!1;this.abort=t.abort,this.cancel=function(t){return null==e?void 0:e(t)},this.cancelRetry=function(){u=!0},this.continueRetry=function(){u=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(t,e){i=t,r=e}));var o=function(e){s.isResolved||(s.isResolved=!0,null==t.onSuccess||t.onSuccess(e),null==n||n(),i(e))},a=function(e){s.isResolved||(s.isResolved=!0,null==t.onError||t.onError(e),null==n||n(),r(e))};!function i(){if(!s.isResolved){var r;try{r=t.fn()}catch(t){r=Promise.reject(t)}e=function(t){if(!s.isResolved&&(a(new E(t)),null==s.abort||s.abort(),F(r)))try{r.cancel()}catch(t){}},s.isTransportCancelable=F(r),Promise.resolve(r).then(o).catch((function(e){var r,o;if(!s.isResolved){var c,l=null!=(r=t.retry)?r:3,h=null!=(o=t.retryDelay)?o:Q,f="function"==typeof h?h(s.failureCount,e):h,d=!0===l||"number"==typeof l&&s.failureCount<l||"function"==typeof l&&l(s.failureCount,e);if(!u&&d)s.failureCount++,null==t.onFail||t.onFail(s.failureCount,e),(c=f,new Promise((function(t){setTimeout(t,c)}))).then((function(){if(!C.isFocused()||!R.isOnline())return new Promise((function(e){n=e,s.isPaused=!0,null==t.onPause||t.onPause()})).then((function(){n=void 0,s.isPaused=!1,null==t.onContinue||t.onContinue()}))})).then((function(){u?a(e):i()}));else a(e)}}))}}()},w=new(function(){function t(){this.queue=[],this.transactions=0,this.notifyFn=function(t){t()},this.batchNotifyFn=function(t){t()}}var e=t.prototype;return e.batch=function(t){var e;this.transactions++;try{e=t()}finally{this.transactions--,this.transactions||this.flush()}return e},e.schedule=function(t){var e=this;this.transactions?this.queue.push(t):P((function(){e.notifyFn(t)}))},e.batchCalls=function(t){var e=this;return function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];e.schedule((function(){t.apply(void 0,i)}))}},e.flush=function(){var t=this,e=this.queue;this.queue=[],e.length&&P((function(){t.batchNotifyFn((function(){e.forEach((function(e){t.notifyFn(e)}))}))}))},e.setNotifyFunction=function(t){this.notifyFn=t},e.setBatchNotifyFunction=function(t){this.batchNotifyFn=t},t}()),A=console;function x(){return A}var D=function(){function t(t){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=t.meta,this.scheduleGc()}var e=t.prototype;return e.setOptions=function(t){var e;this.options=i({},this.defaultOptions,t),this.meta=null==t?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(e=this.options.cacheTime)?e:3e5)},e.setDefaultOptions=function(t){this.defaultOptions=t},e.scheduleGc=function(){var t=this;this.clearGcTimeout(),u(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){t.optionalRemove()}),this.cacheTime))},e.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},e.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},e.setData=function(t,e){var n,i,r=this.state.data,s=function(t,e){return"function"==typeof t?t(e):t}(t,r);return(null==(n=(i=this.options).isDataEqual)?void 0:n.call(i,r,s))?s=r:!1!==this.options.structuralSharing&&(s=m(r,s)),this.dispatch({data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt}),s},e.setState=function(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})},e.cancel=function(t){var e,n=this.promise;return null==(e=this.retryer)||e.cancel(t),n?n.then(s).catch(s):Promise.resolve()},e.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},e.reset=function(){this.destroy(),this.setState(this.initialState)},e.isActive=function(){return this.observers.some((function(t){return!1!==t.options.enabled}))},e.isFetching=function(){return this.state.isFetching},e.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(t){return t.getCurrentResult().isStale}))},e.isStaleByTime=function(t){return void 0===t&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!c(this.state.dataUpdatedAt,t)},e.onFocus=function(){var t,e=this.observers.find((function(t){return t.shouldFetchOnWindowFocus()}));e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.onOnline=function(){var t,e=this.observers.find((function(t){return t.shouldFetchOnReconnect()}));e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.addObserver=function(t){-1===this.observers.indexOf(t)&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},e.removeObserver=function(t){-1!==this.observers.indexOf(t)&&(this.observers=this.observers.filter((function(e){return e!==t})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},e.getObserversCount=function(){return this.observers.length},e.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},e.fetch=function(t,e){var n,i,r,s=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return null==(u=this.retryer)||u.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){var a=this.observers.find((function(t){return t.options.queryFn}));a&&this.setOptions(a.options)}var c=o(this.queryKey),l=q(),h={queryKey:c,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(l)return s.abortSignalConsumed=!0,l.signal}});var f,d,v={fetchOptions:e,options:this.options,queryKey:c,state:this.state,fetchFn:function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(h)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(f=this.options.behavior)||f.onFetch(v));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(i=v.fetchOptions)?void 0:i.meta))||this.dispatch({type:"fetch",meta:null==(d=v.fetchOptions)?void 0:d.meta});return this.retryer=new M({fn:v.fetchFn,abort:null==l||null==(r=l.abort)?void 0:r.bind(l),onSuccess:function(t){s.setData(t),null==s.cache.config.onSuccess||s.cache.config.onSuccess(t,s),0===s.cacheTime&&s.optionalRemove()},onError:function(t){S(t)&&t.silent||s.dispatch({type:"error",error:t}),S(t)||(null==s.cache.config.onError||s.cache.config.onError(t,s),x().error(t)),0===s.cacheTime&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:v.options.retry,retryDelay:v.options.retryDelay}),this.promise=this.retryer.promise,this.promise},e.dispatch=function(t){var e=this;this.state=this.reducer(this.state,t),w.batch((function(){e.observers.forEach((function(e){e.onQueryUpdate(t)})),e.cache.notify({query:e,type:"queryUpdated",action:t})}))},e.getDefaultState=function(t){var e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==t.initialData?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,i=void 0!==e;return{data:e,dataUpdateCount:0,dataUpdatedAt:i?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:i?"success":"idle"}},e.reducer=function(t,e){var n,r;switch(e.type){case"failed":return i({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return i({},t,{isPaused:!0});case"continue":return i({},t,{isPaused:!1});case"fetch":return i({},t,{fetchFailureCount:0,fetchMeta:null!=(n=e.meta)?n:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return i({},t,{data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(r=e.dataUpdatedAt)?r:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var s=e.error;return S(s)&&s.revert&&this.revertState?i({},this.revertState):i({},t,{error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return i({},t,{isInvalidated:!0});case"setState":return i({},t,e.state);default:return t}},t}(),U=function(t){function n(e){var n;return(n=t.call(this)||this).config=e||{},n.queries=[],n.queriesMap={},n}e(n,t);var i=n.prototype;return i.build=function(t,e,n){var i,r=e.queryKey,s=null!=(i=e.queryHash)?i:v(r,e),u=this.get(s);return u||(u=new D({cache:this,queryKey:r,queryHash:s,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(r),meta:e.meta}),this.add(u)),u},i.add=function(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"queryAdded",query:t}))},i.remove=function(t){var e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter((function(e){return e!==t})),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"queryRemoved",query:t}))},i.clear=function(){var t=this;w.batch((function(){t.queries.forEach((function(e){t.remove(e)}))}))},i.get=function(t){return this.queriesMap[t]},i.getAll=function(){return this.queries},i.find=function(t,e){var n=h(t,e)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find((function(t){return f(n,t)}))},i.findAll=function(t,e){var n=h(t,e)[0];return Object.keys(n).length>0?this.queries.filter((function(t){return f(n,t)})):this.queries},i.notify=function(t){var e=this;w.batch((function(){e.listeners.forEach((function(e){e(t)}))}))},i.onFocus=function(){var t=this;w.batch((function(){t.queries.forEach((function(t){t.onFocus()}))}))},i.onOnline=function(){var t=this;w.batch((function(){t.queries.forEach((function(t){t.onOnline()}))}))},n}(n),I=function(){function t(t){this.options=i({},t.defaultOptions,t.options),this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.observers=[],this.state=t.state||T(),this.meta=t.meta}var e=t.prototype;return e.setState=function(t){this.dispatch({type:"setState",state:t})},e.addObserver=function(t){-1===this.observers.indexOf(t)&&this.observers.push(t)},e.removeObserver=function(t){this.observers=this.observers.filter((function(e){return e!==t}))},e.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(s).catch(s)):Promise.resolve()},e.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},e.execute=function(){var t,e=this,n="loading"===this.state.status,i=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),i=i.then((function(){null==e.mutationCache.config.onMutate||e.mutationCache.config.onMutate(e.state.variables,e)})).then((function(){return null==e.options.onMutate?void 0:e.options.onMutate(e.state.variables)})).then((function(t){t!==e.state.context&&e.dispatch({type:"loading",context:t,variables:e.state.variables})}))),i.then((function(){return e.executeMutation()})).then((function(n){t=n,null==e.mutationCache.config.onSuccess||e.mutationCache.config.onSuccess(t,e.state.variables,e.state.context,e)})).then((function(){return null==e.options.onSuccess?void 0:e.options.onSuccess(t,e.state.variables,e.state.context)})).then((function(){return null==e.options.onSettled?void 0:e.options.onSettled(t,null,e.state.variables,e.state.context)})).then((function(){return e.dispatch({type:"success",data:t}),t})).catch((function(t){return null==e.mutationCache.config.onError||e.mutationCache.config.onError(t,e.state.variables,e.state.context,e),x().error(t),Promise.resolve().then((function(){return null==e.options.onError?void 0:e.options.onError(t,e.state.variables,e.state.context)})).then((function(){return null==e.options.onSettled?void 0:e.options.onSettled(void 0,t,e.state.variables,e.state.context)})).then((function(){throw e.dispatch({type:"error",error:t}),t}))}))},e.executeMutation=function(){var t,e=this;return this.retryer=new M({fn:function(){return e.options.mutationFn?e.options.mutationFn(e.state.variables):Promise.reject("No mutationFn found")},onFail:function(){e.dispatch({type:"failed"})},onPause:function(){e.dispatch({type:"pause"})},onContinue:function(){e.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay}),this.retryer.promise},e.dispatch=function(t){var e=this;this.state=function(t,e){switch(e.type){case"failed":return i({},t,{failureCount:t.failureCount+1});case"pause":return i({},t,{isPaused:!0});case"continue":return i({},t,{isPaused:!1});case"loading":return i({},t,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return i({},t,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return i({},t,{data:void 0,error:e.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return i({},t,e.state);default:return t}}(this.state,t),w.batch((function(){e.observers.forEach((function(e){e.onMutationUpdate(t)})),e.mutationCache.notify(e)}))},t}();function T(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}var K=function(t){function n(e){var n;return(n=t.call(this)||this).config=e||{},n.mutations=[],n.mutationId=0,n}e(n,t);var i=n.prototype;return i.build=function(t,e,n){var i=new I({mutationCache:this,mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:n,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0,meta:e.meta});return this.add(i),i},i.add=function(t){this.mutations.push(t),this.notify(t)},i.remove=function(t){this.mutations=this.mutations.filter((function(e){return e!==t})),t.cancel(),this.notify(t)},i.clear=function(){var t=this;w.batch((function(){t.mutations.forEach((function(e){t.remove(e)}))}))},i.getAll=function(){return this.mutations},i.find=function(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find((function(e){return d(t,e)}))},i.findAll=function(t){return this.mutations.filter((function(e){return d(t,e)}))},i.notify=function(t){var e=this;w.batch((function(){e.listeners.forEach((function(e){e(t)}))}))},i.onFocus=function(){this.resumePausedMutations()},i.onOnline=function(){this.resumePausedMutations()},i.resumePausedMutations=function(){var t=this.mutations.filter((function(t){return t.state.isPaused}));return w.batch((function(){return t.reduce((function(t,e){return t.then((function(){return e.continue().catch(s)}))}),Promise.resolve())}))},n}(n);function L(){return{onFetch:function(t){t.fetchFn=function(){var e,n,i,r,s,u,o,a=null==(e=t.fetchOptions)||null==(n=e.meta)?void 0:n.refetchPage,c=null==(i=t.fetchOptions)||null==(r=i.meta)?void 0:r.fetchMore,l=null==c?void 0:c.pageParam,h="forward"===(null==c?void 0:c.direction),f="backward"===(null==c?void 0:c.direction),d=(null==(s=t.state.data)?void 0:s.pages)||[],v=(null==(u=t.state.data)?void 0:u.pageParams)||[],p=q(),y=null==p?void 0:p.signal,m=v,b=!1,g=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},O=function(t,e,n,i){return m=i?[e].concat(m):[].concat(m,[e]),i?[n].concat(t):[].concat(t,[n])},P=function(e,n,i,r){if(b)return Promise.reject("Cancelled");if(void 0===i&&!n&&e.length)return Promise.resolve(e);var s={queryKey:t.queryKey,signal:y,pageParam:i,meta:t.meta},u=g(s),o=Promise.resolve(u).then((function(t){return O(e,i,t,r)}));F(u)&&(o.cancel=u.cancel);return o};if(d.length)if(h){var C=void 0!==l,R=C?l:j(t.options,d);o=P(d,C,R)}else if(f){var Q=void 0!==l,E=Q?l:H(t.options,d);o=P(d,Q,E,!0)}else!function(){m=[];var e=void 0===t.options.getNextPageParam,n=!a||!d[0]||a(d[0],0,d);o=n?P([],e,v[0]):Promise.resolve(O([],v[0],d[0]));for(var i=function(n){o=o.then((function(i){if(!a||!d[n]||a(d[n],n,d)){var r=e?v[n]:j(t.options,i);return P(i,e,r)}return Promise.resolve(O(i,v[n],d[n]))}))},r=1;r<d.length;r++)i(r)}();else o=P([]);var S=o.then((function(t){return{pages:t,pageParams:m}}));return S.cancel=function(){b=!0,null==p||p.abort(),F(o)&&o.cancel()},S}}}}function j(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}function H(t,e){return null==t.getPreviousPageParam?void 0:t.getPreviousPageParam(e[0],e)}function k(t,e){if(t.getNextPageParam&&Array.isArray(e)){var n=j(t,e);return null!=n&&!1!==n}}function N(t,e){if(t.getPreviousPageParam&&Array.isArray(e)){var n=H(t,e);return null!=n&&!1!==n}}var _=function(){function t(t){void 0===t&&(t={}),this.queryCache=t.queryCache||new U,this.mutationCache=t.mutationCache||new K,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var e=t.prototype;return e.mount=function(){var t=this;this.unsubscribeFocus=C.subscribe((function(){C.isFocused()&&R.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())})),this.unsubscribeOnline=R.subscribe((function(){C.isFocused()&&R.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())}))},e.unmount=function(){var t,e;null==(t=this.unsubscribeFocus)||t.call(this),null==(e=this.unsubscribeOnline)||e.call(this)},e.isFetching=function(t,e){var n=h(t,e)[0];return n.fetching=!0,this.queryCache.findAll(n).length},e.isMutating=function(t){return this.mutationCache.findAll(i({},t,{fetching:!0})).length},e.getQueryData=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state.data},e.getQueriesData=function(t){return this.getQueryCache().findAll(t).map((function(t){return[t.queryKey,t.state.data]}))},e.setQueryData=function(t,e,n){var i=l(t),r=this.defaultQueryOptions(i);return this.queryCache.build(this,r).setData(e,n)},e.setQueriesData=function(t,e,n){var i=this;return w.batch((function(){return i.getQueryCache().findAll(t).map((function(t){var r=t.queryKey;return[r,i.setQueryData(r,e,n)]}))}))},e.getQueryState=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state},e.removeQueries=function(t,e){var n=h(t,e)[0],i=this.queryCache;w.batch((function(){i.findAll(n).forEach((function(t){i.remove(t)}))}))},e.resetQueries=function(t,e,n){var r=this,s=h(t,e,n),u=s[0],o=s[1],a=this.queryCache,c=i({},u,{active:!0});return w.batch((function(){return a.findAll(u).forEach((function(t){t.reset()})),r.refetchQueries(c,o)}))},e.cancelQueries=function(t,e,n){var i=this,r=h(t,e,n),u=r[0],o=r[1],a=void 0===o?{}:o;void 0===a.revert&&(a.revert=!0);var c=w.batch((function(){return i.queryCache.findAll(u).map((function(t){return t.cancel(a)}))}));return Promise.all(c).then(s).catch(s)},e.invalidateQueries=function(t,e,n){var r,s,u,o=this,a=h(t,e,n),c=a[0],l=a[1],f=i({},c,{active:null==(r=null!=(s=c.refetchActive)?s:c.active)||r,inactive:null!=(u=c.refetchInactive)&&u});return w.batch((function(){return o.queryCache.findAll(c).forEach((function(t){t.invalidate()})),o.refetchQueries(f,l)}))},e.refetchQueries=function(t,e,n){var r=this,u=h(t,e,n),o=u[0],a=u[1],c=w.batch((function(){return r.queryCache.findAll(o).map((function(t){return t.fetch(void 0,i({},a,{meta:{refetchPage:null==o?void 0:o.refetchPage}}))}))})),l=Promise.all(c).then(s);return(null==a?void 0:a.throwOnError)||(l=l.catch(s)),l},e.fetchQuery=function(t,e,n){var i=l(t,e,n),r=this.defaultQueryOptions(i);void 0===r.retry&&(r.retry=!1);var s=this.queryCache.build(this,r);return s.isStaleByTime(r.staleTime)?s.fetch(r):Promise.resolve(s.state.data)},e.prefetchQuery=function(t,e,n){return this.fetchQuery(t,e,n).then(s).catch(s)},e.fetchInfiniteQuery=function(t,e,n){var i=l(t,e,n);return i.behavior=L(),this.fetchQuery(i)},e.prefetchInfiniteQuery=function(t,e,n){return this.fetchInfiniteQuery(t,e,n).then(s).catch(s)},e.cancelMutations=function(){var t=this,e=w.batch((function(){return t.mutationCache.getAll().map((function(t){return t.cancel()}))}));return Promise.all(e).then(s).catch(s)},e.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},e.executeMutation=function(t){return this.mutationCache.build(this,t).execute()},e.getQueryCache=function(){return this.queryCache},e.getMutationCache=function(){return this.mutationCache},e.getDefaultOptions=function(){return this.defaultOptions},e.setDefaultOptions=function(t){this.defaultOptions=t},e.setQueryDefaults=function(t,e){var n=this.queryDefaults.find((function(e){return p(t)===p(e.queryKey)}));n?n.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})},e.getQueryDefaults=function(t){var e;return t?null==(e=this.queryDefaults.find((function(e){return y(t,e.queryKey)})))?void 0:e.defaultOptions:void 0},e.setMutationDefaults=function(t,e){var n=this.mutationDefaults.find((function(e){return p(t)===p(e.mutationKey)}));n?n.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})},e.getMutationDefaults=function(t){var e;return t?null==(e=this.mutationDefaults.find((function(e){return y(t,e.mutationKey)})))?void 0:e.defaultOptions:void 0},e.defaultQueryOptions=function(t){if(null==t?void 0:t._defaulted)return t;var e=i({},this.defaultOptions.queries,this.getQueryDefaults(null==t?void 0:t.queryKey),t,{_defaulted:!0});return!e.queryHash&&e.queryKey&&(e.queryHash=v(e.queryKey,e)),e},e.defaultQueryObserverOptions=function(t){return this.defaultQueryOptions(t)},e.defaultMutationOptions=function(t){return(null==t?void 0:t._defaulted)?t:i({},this.defaultOptions.mutations,this.getMutationDefaults(null==t?void 0:t.mutationKey),t,{_defaulted:!0})},e.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},t}(),G=function(t){function n(e,n){var i;return(i=t.call(this)||this).client=e,i.options=n,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(n),i}e(n,t);var o=n.prototype;return o.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},o.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),B(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},o.onUnsubscribe=function(){this.listeners.length||this.destroy()},o.shouldFetchOnReconnect=function(){return W(this.currentQuery,this.options,this.options.refetchOnReconnect)},o.shouldFetchOnWindowFocus=function(){return W(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},o.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},o.setOptions=function(t,e){var n=this.options,i=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var r=this.hasListeners();r&&J(this.currentQuery,i,this.options,n)&&this.executeFetch(),this.updateResult(e),!r||this.currentQuery===i&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var s=this.computeRefetchInterval();!r||this.currentQuery===i&&this.options.enabled===n.enabled&&s===this.currentRefetchInterval||this.updateRefetchInterval(s)},o.getOptimisticResult=function(t){var e=this.client.defaultQueryObserverOptions(t),n=this.client.getQueryCache().build(this.client,e);return this.createResult(n,e)},o.getCurrentResult=function(){return this.currentResult},o.trackResult=function(t,e){var n=this,i={},r=function(t){n.trackedProps.includes(t)||n.trackedProps.push(t)};return Object.keys(t).forEach((function(e){Object.defineProperty(i,e,{configurable:!1,enumerable:!0,get:function(){return r(e),t[e]}})})),(e.useErrorBoundary||e.suspense)&&r("error"),i},o.getNextResult=function(t){var e=this;return new Promise((function(n,i){var r=e.subscribe((function(e){e.isFetching||(r(),e.isError&&(null==t?void 0:t.throwOnError)?i(e.error):n(e))}))}))},o.getCurrentQuery=function(){return this.currentQuery},o.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},o.refetch=function(t){return this.fetch(i({},t,{meta:{refetchPage:null==t?void 0:t.refetchPage}}))},o.fetchOptimistic=function(t){var e=this,n=this.client.defaultQueryObserverOptions(t),i=this.client.getQueryCache().build(this.client,n);return i.fetch().then((function(){return e.createResult(i,n)}))},o.fetch=function(t){var e=this;return this.executeFetch(t).then((function(){return e.updateResult(),e.currentResult}))},o.executeFetch=function(t){this.updateQuery();var e=this.currentQuery.fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(s)),e},o.updateStaleTimeout=function(){var t=this;if(this.clearStaleTimeout(),!r&&!this.currentResult.isStale&&u(this.options.staleTime)){var e=c(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){t.currentResult.isStale||t.updateResult()}),e)}},o.computeRefetchInterval=function(){var t;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t},o.updateRefetchInterval=function(t){var e=this;this.clearRefetchInterval(),this.currentRefetchInterval=t,!r&&!1!==this.options.enabled&&u(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(e.options.refetchIntervalInBackground||C.isFocused())&&e.executeFetch()}),this.currentRefetchInterval))},o.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},o.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},o.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},o.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},o.createResult=function(t,e){var n,i=this.currentQuery,r=this.options,s=this.currentResult,u=this.currentResultState,o=this.currentResultOptions,a=t!==i,c=a?t.state:this.currentQueryInitialState,l=a?this.currentResult:this.previousQueryResult,h=t.state,f=h.dataUpdatedAt,d=h.error,v=h.errorUpdatedAt,p=h.isFetching,y=h.status,b=!1,g=!1;if(e.optimisticResults){var O=this.hasListeners(),P=!O&&B(t,e),q=O&&J(t,i,e,r);(P||q)&&(p=!0,f||(y="loading"))}if(e.keepPreviousData&&!h.dataUpdateCount&&(null==l?void 0:l.isSuccess)&&"error"!==y)n=l.data,f=l.dataUpdatedAt,y=l.status,b=!0;else if(e.select&&void 0!==h.data)if(s&&h.data===(null==u?void 0:u.data)&&e.select===this.selectFn)n=this.selectResult;else try{this.selectFn=e.select,n=e.select(h.data),!1!==e.structuralSharing&&(n=m(null==s?void 0:s.data,n)),this.selectResult=n,this.selectError=null}catch(t){x().error(t),this.selectError=t}else n=h.data;if(void 0!==e.placeholderData&&void 0===n&&("loading"===y||"idle"===y)){var C;if((null==s?void 0:s.isPlaceholderData)&&e.placeholderData===(null==o?void 0:o.placeholderData))C=s.data;else if(C="function"==typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&void 0!==C)try{C=e.select(C),!1!==e.structuralSharing&&(C=m(null==s?void 0:s.data,C)),this.selectError=null}catch(t){x().error(t),this.selectError=t}void 0!==C&&(y="success",n=C,g=!0)}return this.selectError&&(d=this.selectError,n=this.selectResult,v=Date.now(),y="error"),{status:y,isLoading:"loading"===y,isSuccess:"success"===y,isError:"error"===y,isIdle:"idle"===y,data:n,dataUpdatedAt:f,error:d,errorUpdatedAt:v,failureCount:h.fetchFailureCount,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>c.dataUpdateCount||h.errorUpdateCount>c.errorUpdateCount,isFetching:p,isRefetching:p&&"loading"!==y,isLoadingError:"error"===y&&0===h.dataUpdatedAt,isPlaceholderData:g,isPreviousData:b,isRefetchError:"error"===y&&0!==h.dataUpdatedAt,isStale:z(t,e),refetch:this.refetch,remove:this.remove}},o.shouldNotifyListeners=function(t,e){if(!e)return!0;var n=this.options,i=n.notifyOnChangeProps,r=n.notifyOnChangePropsExclusions;if(!i&&!r)return!0;if("tracked"===i&&!this.trackedProps.length)return!0;var s="tracked"===i?this.trackedProps:i;return Object.keys(t).some((function(n){var i=n,u=t[i]!==e[i],o=null==s?void 0:s.some((function(t){return t===n})),a=null==r?void 0:r.some((function(t){return t===n}));return u&&!a&&(!s||o)}))},o.updateResult=function(t){var e=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!function(t,e){if(t&&!e||e&&!t)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}(this.currentResult,e)){var n={cache:!0};!1!==(null==t?void 0:t.listeners)&&this.shouldNotifyListeners(this.currentResult,e)&&(n.listeners=!0),this.notify(i({},n,t))}},o.updateQuery=function(){var t=this.client.getQueryCache().build(this.client,this.options);if(t!==this.currentQuery){var e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}},o.onQueryUpdate=function(t){var e={};"success"===t.type?e.onSuccess=!0:"error"!==t.type||S(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()},o.notify=function(t){var e=this;w.batch((function(){t.onSuccess?(null==e.options.onSuccess||e.options.onSuccess(e.currentResult.data),null==e.options.onSettled||e.options.onSettled(e.currentResult.data,null)):t.onError&&(null==e.options.onError||e.options.onError(e.currentResult.error),null==e.options.onSettled||e.options.onSettled(void 0,e.currentResult.error)),t.listeners&&e.listeners.forEach((function(t){t(e.currentResult)})),t.cache&&e.client.getQueryCache().notify({query:e.currentQuery,type:"observerResultsUpdated"})}))},n}(n);function B(t,e){return function(t,e){return!(!1===e.enabled||t.state.dataUpdatedAt||"error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&W(t,e,e.refetchOnMount)}function W(t,e,n){if(!1!==e.enabled){var i="function"==typeof n?n(t):n;return"always"===i||!1!==i&&z(t,e)}return!1}function J(t,e,n,i){return!1!==n.enabled&&(t!==e||!1===i.enabled)&&(!n.suspense||"error"!==t.state.status)&&z(t,n)}function z(t,e){return t.isStaleByTime(e.staleTime)}var V=function(t){function n(e,n){var i;return(i=t.call(this)||this).client=e,i.queries=[],i.result=[],i.observers=[],i.observersMap={},n&&i.setQueries(n),i}e(n,t);var i=n.prototype;return i.onSubscribe=function(){var t=this;1===this.listeners.length&&this.observers.forEach((function(e){e.subscribe((function(n){t.onUpdate(e,n)}))}))},i.onUnsubscribe=function(){this.listeners.length||this.destroy()},i.destroy=function(){this.listeners=[],this.observers.forEach((function(t){t.destroy()}))},i.setQueries=function(t,e){this.queries=t,this.updateObservers(e)},i.getCurrentResult=function(){return this.result},i.getOptimisticResult=function(t){return this.findMatchingObservers(t).map((function(t){return t.observer.getOptimisticResult(t.defaultedQueryOptions)}))},i.findMatchingObservers=function(t){var e=this,n=this.observers,i=t.map((function(t){return e.client.defaultQueryObserverOptions(t)})),r=i.flatMap((function(t){var e=n.find((function(e){return e.options.queryHash===t.queryHash}));return null!=e?[{defaultedQueryOptions:t,observer:e}]:[]})),s=r.map((function(t){return t.defaultedQueryOptions.queryHash})),u=i.filter((function(t){return!s.includes(t.queryHash)})),o=n.filter((function(t){return!r.some((function(e){return e.observer===t}))})),a=u.map((function(t,n){if(t.keepPreviousData){var i=o[n];if(void 0!==i)return{defaultedQueryOptions:t,observer:i}}return{defaultedQueryOptions:t,observer:e.getObserver(t)}}));return r.concat(a).sort((function(t,e){return i.indexOf(t.defaultedQueryOptions)-i.indexOf(e.defaultedQueryOptions)}))},i.getObserver=function(t){var e=this.client.defaultQueryObserverOptions(t),n=this.observersMap[e.queryHash];return null!=n?n:new G(this.client,e)},i.updateObservers=function(t){var e=this;w.batch((function(){var n=e.observers,i=e.findMatchingObservers(e.queries);i.forEach((function(e){return e.observer.setOptions(e.defaultedQueryOptions,t)}));var r=i.map((function(t){return t.observer})),s=Object.fromEntries(r.map((function(t){return[t.options.queryHash,t]}))),u=r.map((function(t){return t.getCurrentResult()})),o=r.some((function(t,e){return t!==n[e]}));(n.length!==r.length||o)&&(e.observers=r,e.observersMap=s,e.result=u,e.hasListeners()&&(a(n,r).forEach((function(t){t.destroy()})),a(r,n).forEach((function(t){t.subscribe((function(n){e.onUpdate(t,n)}))})),e.notify()))}))},i.onUpdate=function(t,e){var n=this.observers.indexOf(t);-1!==n&&(this.result=function(t,e,n){var i=t.slice(0);return i[e]=n,i}(this.result,n,e),this.notify())},i.notify=function(){var t=this;w.batch((function(){t.listeners.forEach((function(e){e(t.result)}))}))},n}(n),X=function(t){function n(e,n){return t.call(this,e,n)||this}e(n,t);var r=n.prototype;return r.bindMethods=function(){t.prototype.bindMethods.call(this),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)},r.setOptions=function(e,n){t.prototype.setOptions.call(this,i({},e,{behavior:L()}),n)},r.getOptimisticResult=function(e){return e.behavior=L(),t.prototype.getOptimisticResult.call(this,e)},r.fetchNextPage=function(t){var e;return this.fetch({cancelRefetch:null==(e=null==t?void 0:t.cancelRefetch)||e,throwOnError:null==t?void 0:t.throwOnError,meta:{fetchMore:{direction:"forward",pageParam:null==t?void 0:t.pageParam}}})},r.fetchPreviousPage=function(t){var e;return this.fetch({cancelRefetch:null==(e=null==t?void 0:t.cancelRefetch)||e,throwOnError:null==t?void 0:t.throwOnError,meta:{fetchMore:{direction:"backward",pageParam:null==t?void 0:t.pageParam}}})},r.createResult=function(e,n){var r,s,u,o,a,c,l=e.state;return i({},t.prototype.createResult.call(this,e,n),{fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:k(n,null==(r=l.data)?void 0:r.pages),hasPreviousPage:N(n,null==(s=l.data)?void 0:s.pages),isFetchingNextPage:l.isFetching&&"forward"===(null==(u=l.fetchMeta)||null==(o=u.fetchMore)?void 0:o.direction),isFetchingPreviousPage:l.isFetching&&"backward"===(null==(a=l.fetchMeta)||null==(c=a.fetchMore)?void 0:c.direction)})},n}(G),Y=function(t){function n(e,n){var i;return(i=t.call(this)||this).client=e,i.setOptions(n),i.bindMethods(),i.updateResult(),i}e(n,t);var r=n.prototype;return r.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},r.setOptions=function(t){this.options=this.client.defaultMutationOptions(t)},r.onUnsubscribe=function(){var t;this.listeners.length||(null==(t=this.currentMutation)||t.removeObserver(this))},r.onMutationUpdate=function(t){this.updateResult();var e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)},r.getCurrentResult=function(){return this.currentResult},r.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},r.mutate=function(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,i({},this.options,{variables:void 0!==t?t:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},r.updateResult=function(){var t=this.currentMutation?this.currentMutation.state:{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},e=i({},t,{isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset});this.currentResult=e},r.notify=function(t){var e=this;w.batch((function(){e.mutateOptions&&(t.onSuccess?(null==e.mutateOptions.onSuccess||e.mutateOptions.onSuccess(e.currentResult.data,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(e.currentResult.data,null,e.currentResult.variables,e.currentResult.context)):t.onError&&(null==e.mutateOptions.onError||e.mutateOptions.onError(e.currentResult.error,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(void 0,e.currentResult.error,e.currentResult.variables,e.currentResult.context))),t.listeners&&e.listeners.forEach((function(t){t(e.currentResult)}))}))},n}(n);function Z(t){return t.state.isPaused}function $(t){return"success"===t.state.status}t.CancelledError=E,t.InfiniteQueryObserver=X,t.MutationCache=K,t.MutationObserver=Y,t.QueriesObserver=V,t.QueryCache=U,t.QueryClient=_,t.QueryObserver=G,t.dehydrate=function(t,e){var n,i,r=[],s=[];if(!1!==(null==(n=e=e||{})?void 0:n.dehydrateMutations)){var u=e.shouldDehydrateMutation||Z;t.getMutationCache().getAll().forEach((function(t){u(t)&&r.push(function(t){return{mutationKey:t.options.mutationKey,state:t.state}}(t))}))}if(!1!==(null==(i=e)?void 0:i.dehydrateQueries)){var o=e.shouldDehydrateQuery||$;t.getQueryCache().getAll().forEach((function(t){o(t)&&s.push(function(t){return{state:t.state,queryKey:t.queryKey,queryHash:t.queryHash}}(t))}))}return{mutations:r,queries:s}},t.focusManager=C,t.hashQueryKey=p,t.hydrate=function(t,e,n){if("object"==typeof e&&null!==e){var r=t.getMutationCache(),s=t.getQueryCache(),u=e.mutations||[],o=e.queries||[];u.forEach((function(e){var s;r.build(t,i({},null==n||null==(s=n.defaultOptions)?void 0:s.mutations,{mutationKey:e.mutationKey}),e.state)})),o.forEach((function(e){var r,u=s.get(e.queryHash);u?u.state.dataUpdatedAt<e.state.dataUpdatedAt&&u.setState(e.state):s.build(t,i({},null==n||null==(r=n.defaultOptions)?void 0:r.queries,{queryKey:e.queryKey,queryHash:e.queryHash}),e.state)}))}},t.isCancelledError=S,t.isError=function(t){return t instanceof Error},t.notifyManager=w,t.onlineManager=R,t.setLogger=function(t){A=t},Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=react-query-core.production.min.js.map
